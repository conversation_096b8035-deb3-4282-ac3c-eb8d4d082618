import os
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup

# 設定
target_url = "https://ja.wikipedia.org/wiki/ウェブスクレイピング"  # 取得したいURL
visited_urls = set()  # 訪問済みのURLを管理
output_dir = "scraped_text"  # テキストを保存するディレクトリ

# ディレクトリが存在しない場合は作成
if not os.path.exists(output_dir):
    os.makedirs(output_dir)


def get_text_from_url(url):
    """
    指定したURLのHTMLから本文テキストを抽出
    """
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # エラーレスポンスをチェック

        soup = BeautifulSoup(response.text, "html.parser")

        # ページの本文部分のテキストを抽出
        # 一般的なウェブサイトの本文は、<article>, <main>, <div class="content"> などに囲まれていることが多い
        # 例として<body>全体から抽出
        body_text = soup.body.get_text(separator=" ", strip=True)
        return body_text

    except requests.exceptions.RequestException as e:
        print(f"Error accessing {url}: {e}")
        return None


def find_links(url, html_content):
    """
    HTMLコンテンツから同一ドメイン内のリンクを見つける
    """
    soup = BeautifulSoup(html_content, "html.parser")
    links = set()
    base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"

    for a_tag in soup.find_all("a", href=True):
        href = a_tag["href"]
        full_url = urljoin(url, href)

        # 同一ドメイン内のリンクのみを対象
        if urlparse(full_url).netloc == urlparse(base_url).netloc:
            # フラグメント（#...）を取り除く
            clean_url = full_url.split("#")[0]
            links.add(clean_url)

    return links


def crawl(url):
    """
    再帰的にウェブページを巡回
    """
    # 既に訪問済み、または対象URLのドメイン外の場合はスキップ
    if url in visited_urls or urlparse(url).netloc != urlparse(target_url).netloc:
        return

    print(f"Crawling: {url}")
    visited_urls.add(url)

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        html_content = response.text

        # テキストを抽出してファイルに保存
        page_text = get_text_from_url(url)
        if page_text:
            # ファイル名として安全な文字列に変換
            file_name = os.path.basename(urlparse(url).path) or "index"
            if not file_name.endswith(".txt"):
                file_name += ".txt"
            file_path = os.path.join(output_dir, file_name)

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(page_text)
            print(f"Saved: {file_path}")

        # 次に巡回するリンクを見つける
        links_to_visit = find_links(url, html_content)

        # 各リンクを再帰的に巡回
        for link in links_to_visit:
            crawl(link)

    except requests.exceptions.RequestException as e:
        print(f"Error crawling {url}: {e}")


# クロールを開始
crawl(target_url)
