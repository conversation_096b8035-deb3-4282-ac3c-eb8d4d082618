import argparse
import os
import time
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup

# コマンドライン引数の解析
parser = argparse.ArgumentParser(
    description="ウェブサイトを再帰的にスクレイピングしてテキストを抽出します"
)
parser.add_argument("url", help="スクレイピング対象のURL")
parser.add_argument(
    "--output-dir",
    "-o",
    default="scraped_text",
    help="テキストを保存するディレクトリ (デフォルト: scraped_text)",
)
args = parser.parse_args()

# 設定
target_url = args.url  # コマンドライン引数から取得
visited_urls = set()  # 訪問済みのURLを管理
output_dir = args.output_dir  # テキストを保存するディレクトリ

# 出力ファイル名を開始URLから決定
start_page_name = os.path.basename(urlparse(target_url).path) or "index"
if not start_page_name.endswith(".txt"):
    start_page_name += ".txt"
output_file_path = os.path.join(output_dir, start_page_name)

# ディレクトリが存在しない場合は作成
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 出力ファイルを初期化（既存ファイルがあれば上書き）
with open(output_file_path, "w", encoding="utf-8") as f:
    f.write("")  # 空ファイルで初期化


def get_text_from_url(url):
    """
    指定したURLのHTMLから本文テキストを抽出
    """
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # エラーレスポンスをチェック

        soup = BeautifulSoup(response.text, "html.parser")

        # ページの本文部分のテキストを抽出
        # 一般的なウェブサイトの本文は、<article>, <main>, <div class="content"> などに囲まれていることが多い
        # 例として<body>全体から抽出
        body_text = soup.body.get_text(separator=" ", strip=True)
        return body_text

    except requests.exceptions.RequestException as e:
        print(f"Error accessing {url}: {e}")
        print("処理を中止します。")
        raise  # 例外を再発生させて処理を停止


def find_links(url, html_content):
    """
    HTMLコンテンツから同一ドメイン内のリンクを見つける
    """
    soup = BeautifulSoup(html_content, "html.parser")
    links = set()
    base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"

    for a_tag in soup.find_all("a", href=True):
        href = a_tag["href"]
        full_url = urljoin(url, href)

        # 同一ドメイン内のリンクのみを対象
        if urlparse(full_url).netloc == urlparse(base_url).netloc:
            # フラグメント（#...）を取り除く
            clean_url = full_url.split("#")[0]
            links.add(clean_url)

    return links


def collect_all_urls(url, collected_urls=None):
    """
    再帰的にすべてのURLを収集（実際のクロールは行わない）
    """
    if collected_urls is None:
        collected_urls = set()

    # 既に収集済み、または対象URLのドメイン外の場合はスキップ
    if url in collected_urls or urlparse(url).netloc != urlparse(target_url).netloc:
        return collected_urls

    collected_urls.add(url)

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        html_content = response.text

        # リンクを見つける
        links_to_visit = find_links(url, html_content)

        # 各リンクを再帰的に収集
        for link in links_to_visit:
            collect_all_urls(link, collected_urls)

    except requests.exceptions.RequestException as e:
        print(f"Warning: Could not access {url}: {e}")

    return collected_urls


def crawl(url):
    """
    再帰的にウェブページを巡回
    """
    # 既に訪問済み、または対象URLのドメイン外の場合はスキップ
    if url in visited_urls or urlparse(url).netloc != urlparse(target_url).netloc:
        return

    print(f"Crawling: {url}")
    visited_urls.add(url)

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        html_content = response.text

        # テキストを抽出して1つのファイルに追記
        page_text = get_text_from_url(url)
        if page_text:
            # ページの区切りとURLを追加
            separator = f"\n{'=' * 80}\n"
            separator += f"URL: {url}\n"
            separator += f"{'=' * 80}\n\n"

            with open(output_file_path, "a", encoding="utf-8") as f:
                f.write(separator)
                f.write(page_text)
                f.write("\n\n")
            print(f"Added to file: {url}")

        # 次に巡回するリンクを見つける
        links_to_visit = find_links(url, html_content)

        # サーバーへの負荷軽減のため3秒待機
        time.sleep(3)

        # 各リンクを再帰的に巡回
        for link in links_to_visit:
            crawl(link)

    except requests.exceptions.RequestException as e:
        print(f"Error crawling {url}: {e}")
        print("処理を中止します。")
        raise  # 例外を再発生させて処理を停止


# まず、クロール対象のURLを収集
print(f"クロール対象のURL収集中: {target_url}")
print("しばらくお待ちください...")
print()

try:
    all_urls = collect_all_urls(target_url)

    print(f"クロール対象のページ一覧 (合計: {len(all_urls)}ページ):")
    print("=" * 60)
    for i, url in enumerate(sorted(all_urls), 1):
        print(f"{i:3d}. {url}")
    print("=" * 60)
    print()
    print(f"出力ファイル: {output_file_path}")
    print("3秒間隔でアクセスします")
    print()

    # ユーザーに実行確認
    while True:
        user_input = input("これらのページをクロールしますか？ (y/n): ").lower().strip()
        if user_input in ["y", "yes"]:
            break
        elif user_input in ["n", "no"]:
            print("クロールを中止しました。")
            exit(0)
        else:
            print("'y' または 'n' を入力してください。")

    print()
    print("クロール開始...")
    print()

    # 実際のクロールを開始
    crawl(target_url)
    print()
    print("クロール処理が正常に完了しました。")

except requests.exceptions.RequestException as e:
    print(f"処理中にエラーが発生しました: {e}")
    print("プログラムを終了します。")
except KeyboardInterrupt:
    print("\nユーザーによって中断されました。")
except Exception as e:
    print(f"予期しないエラーが発生しました: {e}")
    print("プログラムを終了します。")
