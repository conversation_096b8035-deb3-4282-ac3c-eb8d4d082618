import argparse
import os
import time
import warnings
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup, XMLParsedAsHTMLWarning

# XMLParsedAsHTMLWarningを無視
warnings.filterwarnings("ignore", category=XMLParsedAsHTMLWarning)

# コマンドライン引数の解析
parser = argparse.ArgumentParser(
    description="ウェブサイトを再帰的にスクレイピングしてテキストを抽出します"
)
parser.add_argument("url", help="スクレイピング対象のURL")
parser.add_argument(
    "--output-dir",
    "-o",
    default="scraped_text",
    help="テキストを保存するディレクトリ (デフォルト: scraped_text)",
)
args = parser.parse_args()

# 設定
target_url = args.url  # コマンドライン引数から取得
visited_urls = set()  # 訪問済みのURLを管理
output_dir = args.output_dir  # テキストを保存するディレクトリ

# 出力ファイル名を開始URLから決定
start_page_name = os.path.basename(urlparse(target_url).path) or "index"
if not start_page_name.endswith(".txt"):
    start_page_name += ".txt"
output_file_path = os.path.join(output_dir, start_page_name)

# ディレクトリが存在しない場合は作成
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 出力ファイルを初期化（既存ファイルがあれば上書き）
with open(output_file_path, "w", encoding="utf-8") as f:
    f.write("")  # 空ファイルで初期化


def get_text_from_url(url):
    """
    指定したURLのHTMLから本文テキストを抽出
    """
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # エラーレスポンスをチェック

        soup = BeautifulSoup(response.text, "html.parser")

        # Zenn特有の記事コンテンツを探す
        article_content = None

        # 記事本文を含む可能性のあるセレクタを試す
        selectors = [
            "article",
            '[data-content="true"]',
            ".znc",
            ".markdown-body",
            "main article",
            '[role="main"]',
            ".content",
            "#__next main",
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                # 最も長いテキストを持つ要素を選択
                best_element = max(elements, key=lambda x: len(x.get_text(strip=True)))
                candidate_text = best_element.get_text(separator=" ", strip=True)
                if len(candidate_text) > 100:  # 十分な長さがある場合
                    article_content = candidate_text
                    print(
                        f"Debug: {url} - セレクタ '{selector}' でコンテンツ発見 ({len(candidate_text)}文字)"
                    )
                    break

        # 記事コンテンツが見つからない場合はbody全体を使用
        if not article_content:
            if soup.body:
                article_content = soup.body.get_text(separator=" ", strip=True)
            else:
                article_content = soup.get_text(separator=" ", strip=True)
            print(f"Debug: {url} - デフォルト抽出 ({len(article_content)}文字)")

        return article_content

    except requests.exceptions.RequestException as e:
        print(f"Error accessing {url}: {e}")
        print("処理を中止します。")
        raise  # 例外を再発生させて処理を停止


def find_links(url, html_content):
    """
    HTMLコンテンツから対象URLパスで始まるリンクを見つける
    """
    soup = BeautifulSoup(html_content, "html.parser")
    links = set()

    # 開始URLのパスを取得（クエリパラメータとフラグメントを除く）
    target_parsed = urlparse(target_url)
    target_base_url = (
        f"{target_parsed.scheme}://{target_parsed.netloc}{target_parsed.path}"
    )
    # 末尾のスラッシュを統一
    if target_base_url.endswith("/"):
        target_base_url = target_base_url[:-1]

    for a_tag in soup.find_all("a", href=True):
        href = a_tag["href"]
        full_url = urljoin(url, href)

        # フラグメント（#...）を取り除く
        clean_url = full_url.split("#")[0]
        # クエリパラメータも取り除く
        clean_url = clean_url.split("?")[0]
        # 末尾のスラッシュを統一
        if clean_url.endswith("/"):
            clean_url = clean_url[:-1]

        # 対象URLパスで始まるリンクのみを対象
        if clean_url.startswith(target_base_url):
            links.add(clean_url)

    return links


def is_target_url(url):
    """
    URLが対象範囲内かどうかを判定
    """
    # 開始URLのパスを取得
    target_parsed = urlparse(target_url)
    target_base_url = (
        f"{target_parsed.scheme}://{target_parsed.netloc}{target_parsed.path}"
    )
    if target_base_url.endswith("/"):
        target_base_url = target_base_url[:-1]

    # チェック対象URLを正規化
    clean_url = url.split("#")[0].split("?")[0]
    if clean_url.endswith("/"):
        clean_url = clean_url[:-1]

    return clean_url.startswith(target_base_url)


def collect_all_urls(url, collected_urls=None):
    """
    再帰的にすべてのURLを収集（実際のクロールは行わない）
    """
    if collected_urls is None:
        collected_urls = set()

    # 既に収集済み、または対象URL範囲外の場合はスキップ
    if url in collected_urls or not is_target_url(url):
        return collected_urls

    collected_urls.add(url)

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        html_content = response.text

        # リンクを見つける
        links_to_visit = find_links(url, html_content)

        # 各リンクを再帰的に収集
        for link in links_to_visit:
            collect_all_urls(link, collected_urls)

    except requests.exceptions.RequestException as e:
        print(f"Warning: Could not access {url}: {e}")

    return collected_urls


def crawl(url):
    """
    再帰的にウェブページを巡回
    """
    # 既に訪問済み、または対象URL範囲外の場合はスキップ
    if url in visited_urls or not is_target_url(url):
        return

    print(f"Crawling: {url}")
    visited_urls.add(url)

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        html_content = response.text

        # テキストを抽出して1つのファイルに追記
        page_text = get_text_from_url(url)
        if page_text:
            # ページの区切りとURLを追加
            separator = f"\n{'=' * 80}\n"
            separator += f"URL: {url}\n"
            separator += f"{'=' * 80}\n\n"

            with open(output_file_path, "a", encoding="utf-8") as f:
                f.write(separator)
                f.write(page_text)
                f.write("\n\n")
            print(f"Added to file: {url} (テキスト長: {len(page_text)}文字)")
        else:
            print(f"Warning: テキストが取得できませんでした: {url}")

        # 次に巡回するリンクを見つける
        links_to_visit = find_links(url, html_content)

        # サーバーへの負荷軽減のため3秒待機
        time.sleep(3)

        # 各リンクを再帰的に巡回
        for link in links_to_visit:
            crawl(link)

    except requests.exceptions.RequestException as e:
        print(f"Error crawling {url}: {e}")
        print("処理を中止します。")
        raise  # 例外を再発生させて処理を停止


# まず、クロール対象のURLを収集
print(f"クロール対象のURL収集中: {target_url}")
print("しばらくお待ちください...")
print()

try:
    all_urls = collect_all_urls(target_url)

    print(f"クロール対象のページ一覧 (合計: {len(all_urls)}ページ):")
    print("=" * 60)
    for i, url in enumerate(sorted(all_urls), 1):
        print(f"{i:3d}. {url}")
    print("=" * 60)
    print()
    print(f"出力ファイル: {output_file_path}")
    print("3秒間隔でアクセスします")
    print()

    # ユーザーに実行確認
    while True:
        user_input = input("これらのページをクロールしますか？ (y/n): ").lower().strip()
        if user_input in ["y", "yes"]:
            break
        elif user_input in ["n", "no"]:
            print("クロールを中止しました。")
            exit(0)
        else:
            print("'y' または 'n' を入力してください。")

    print()
    print("クロール開始...")
    print()

    # 実際のクロールを開始
    crawl(target_url)
    print()
    print("クロール処理が正常に完了しました。")

except requests.exceptions.RequestException as e:
    print(f"処理中にエラーが発生しました: {e}")
    print("プログラムを終了します。")
except KeyboardInterrupt:
    print("\nユーザーによって中断されました。")
except Exception as e:
    print(f"予期しないエラーが発生しました: {e}")
    print("プログラムを終了します。")
