
================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on
================================================================================

Zenn ヤマユ ¥0 今すぐ読む 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン ヤマユ 無料で読める本 TypeScriptとドメイン駆動設計（DDD）を組み合わせ、APIを構築するハンズオンガイドです。この本では、DDDとは何かという基礎的なところからソフトウェア開発における戦略的設計、戦術的設計まで、包括的な知識を提供します。

戦略的設計では、ビジネスの要求に合わせたドメインモデルの設計をイベントストーミングを用いて行います。その後、戦術的設計では、具体的なコードの実装に関連するDDDの原則と実践を学びます。

TypeScriptを使ってコードを書きながら、DDDの概念を実際のプロジェクトに適用するヒントを紹介します。 Chapters Chapter 01 はじめに Chapter 02 ドメイン駆動設計 (DDD) とは Chapter 03 第1部 戦略的設計 (ドメインモデリング) Chapter 04 ドメインモデリング Chapter 05 イベントストーミング Chapter 06 ドメインモデル図の作成 Chapter 07 第2部 戦術的設計 (コード実装) Chapter 08 値オブジェクト (Value Object) Chapter 09 エンティティ (Entity) Chapter 10 集約 (Aggregate) Chapter 11 ドメインサービス (Domain Service) Chapter 12 リポジトリ (Repository) Chapter 13 アプリケーションサービス (Application Service) Chapter 14 プレゼンテーション (Presentation) Chapter 15 第3部 拡張性とメンテナンス Chapter 16 ESLintで不正な依存関係を防ぐ Chapter 17 DI コンテナで依存関係を切り替える Chapter 18 ドメインイベントの活用 Author ヤマユ フロントエンドエンジニア
「スケール」という言葉が好きでよく使います Topics TypeScript アーキテクチャ DDD アジャイル ドメイン駆動設計 ¥0 今すぐ読む 公開 2024/01/10 本文更新 2024/05/08 文章量 約 181,619 字 価格 0 円 ポスト Zenn エンジニアのための 情報共有コミュニティ About Zennについて 運営会社 お知らせ・リリース イベント Guides 使い方 法人向けメニュー New Publication / Pro よくある質問 Links X(Twitter) GitHub メディアキット Legal 利用規約 プライバシーポリシー 特商法表記


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter9_entity
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 09 エンティティ (Entity) ヤマユ 2024.05.08に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter17_di_container
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 17 DI コンテナで依存関係を切り替える ヤマユ 2024.01.21に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter1_intro
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 01 はじめに ヤマユ 2023.12.28に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter14_presentation
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 14 プレゼンテーション (Presentation) ヤマユ 2024.01.15に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter16_eslint
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 16 ESLintで不正な依存関係を防ぐ ヤマユ 2024.01.09に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter12_repository
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 12 リポジトリ (Repository) ヤマユ 2024.01.09に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter13_application_service
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 13 アプリケーションサービス (Application Service) ヤマユ 2024.05.08に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter5_event_storming
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 05 イベントストーミング ヤマユ 2024.01.09に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter7_tactical_design
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 07 第2部 戦術的設計 (コード実装) ヤマユ 2024.01.12に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter2_what_is_ddd
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 02 ドメイン駆動設計 (DDD) とは ヤマユ 2024.01.12に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter10_aggregate
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 10 集約 (Aggregate) ヤマユ 2024.01.09に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter15_scalability
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 15 第3部 拡張性とメンテナンス ヤマユ 2023.12.28に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter11_domain_service
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 11 ドメインサービス (Domain Service) ヤマユ 2024.01.09に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter18_domain_event
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 18 ドメインイベントの活用 ヤマユ 2024.01.18に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter4_domain_modeling
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 04 ドメインモデリング ヤマユ 2024.01.12に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter8_value_object
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 08 値オブジェクト (Value Object) ヤマユ 2024.01.12に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter3_strategic_design
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 03 第1部 戦略的設計 (ドメインモデリング) ヤマユ 2024.01.14に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter6_model
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 06 ドメインモデル図の作成 ヤマユ 2024.01.14に更新

