
================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on
================================================================================

Chapters Chapter 01 はじめに Chapter 02 ドメイン駆動設計 (DDD) とは Chapter 03 第1部 戦略的設計 (ドメインモデリング) Chapter 04 ドメインモデリング Chapter 05 イベントストーミング Chapter 06 ドメインモデル図の作成 Chapter 07 第2部 戦術的設計 (コード実装) Chapter 08 値オブジェクト (Value Object) Chapter 09 エンティティ (Entity) Chapter 10 集約 (Aggregate) Chapter 11 ドメインサービス (Domain Service) Chapter 12 リポジトリ (Repository) Chapter 13 アプリケーションサービス (Application Service) Chapter 14 プレゼンテーション (Presentation) Chapter 15 第3部 拡張性とメンテナンス Chapter 16 ESLintで不正な依存関係を防ぐ Chapter 17 DI コンテナで依存関係を切り替える Chapter 18 ドメインイベントの活用


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter3_strategic_design
================================================================================

このチャプターの目次 戦略的設計とは まとめ 戦略的設計とは 戦略的設計とは、大規模なソフトウェアシステムの開発において、システム全体をどのように構築するかという高レベルの設計アプローチを指します。このアプローチは、システム全体を理解し、整理することで、より効果的で持続可能なソフトウェアの開発を目指します。 戦略的設計の主要な要素 ドメインモデリングとドメインの識別 システム全体を理解するために、ビジネスの核となるコアドメインを識別し、その中のサブドメインを定義します。コアドメイン、サブドメインの詳細は chapter04 ドメインモデリング で説明します。 境界づけられたコンテキストの設定 各サブドメインに対して、そのドメインモデルが適用される特定のコンテキスト (境界づけられたコンテキスト) を設定します。これは、ドメインモデルが適用される範囲を明確にし、他のドメインやシステムとのインターフェイスの境界を定義します。 コンテキスト間の関係の管理 異なる境界づけられたコンテキスト間の関係を明確にし、それらがどのように相互作用するかを定義します。これには、パートナーシップ、共有カーネル、顧客/供給者の開発、腐敗防止層、公開ホストサービスなどがあります。 コンテキストの統合 システム全体の構造を理解し、サブドメインと境界づけられたコンテキストを統合する方法を計画します。これにより、システムが一貫性を保ちながらも、部分ごとに柔軟に発展できるようにします。 ! この本では、コンテキスト間の関係の管理については複雑性が増すため扱いません。基本的にはコンテキスト単位でマイクロサービスとして切り出す形になると考えられます。コンテキストの統合についてはマイクロサービスアーキテクチャを参考にすると良いでしょう。 まとめ それでは次章から戦略的設計の具体的なアプローチを詳しく見ていきましょう。 PREV ドメイン駆動設計 (DDD) とは NEXT ドメインモデリング GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter13_application_service
================================================================================

このチャプターの目次 アプリケーションサービスとは 書籍登録アプリケーションサービスの実装 書籍登録アプリケーションサービスのテスト 書籍取得アプリケーションサービスの実装 DTO (Data Transfer Object) 書籍取得アプリケーションサービスのテスト 在庫追加アプリケーションサービスの実装 在庫追加アプリケーションサービスのテスト 書籍削除アプリケーションサービスの実装 書籍削除アプリケーションサービスのテスト まとめ これまでのコード アプリケーションサービスとは アプリケーションサービス (Application Service) とは、ドメインサービスに次ぐ 2 つ目のサービスで、 ユースケースを実現 するための操作を提供するサービスです。アプリケーションサービスは、 ドメイン層の「エンティティ」「値オブジェクト」「ドメインサービス」などのドメインオブジェクトを利用してユースケースを実現 します。 「ユースケース」とは、ユーザーがシステムを利用する際に実現したい機能や処理のことです。たとえば、在庫管理コンテキストでは、 書籍を登録する 書籍を取得する 在庫を追加する 書籍を削除する などの、いわゆる CRUD 操作がユースケースにあたります。 では、それぞれのユースケースを表現するアプリケーションサービスを実装していきましょう。 書籍登録アプリケーションサービスの実装 それでは、「書籍を登録する」ユースケースを実現するアプリケーションサービスを実装していきましょう。 Application ディレクトリ配下に Book/RegisterBookApplicationService ディレクトリを作成します。次に RegisterBookApplicationService.ts ファイルを作成し、以下のように実装します。 .../Application/Book/RegisterBookApplicationService/RegisterBookApplicationService.ts import { ITransactionManager } from 'Application/shared/ITransactionManager' ; import { Book } from 'Domain/models/Book/Book' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { IBookRepository } from 'Domain/models/Book/IBookRepository' ; import { Price } from 'Domain/models/Book/Price/Price' ; import { Title } from 'Domain/models/Book/Title/Title' ; import { ISBNDuplicationCheckDomainService } from 'Domain/services/Book/ISBNDuplicationCheckDomainService/ISBNDuplicationCheckDomainService' ; export type RegisterBookCommand = { isbn : string ; title : string ; priceAmount : number ; } ; export class RegisterBookApplicationService { constructor ( private bookRepository : IBookRepository , private transactionManager : ITransactionManager ) { } async execute ( command : RegisterBookCommand ) : Promise < void > { await this . transactionManager . begin ( async ( ) => { const isDuplicateISBN = await new ISBNDuplicationCheckDomainService ( this . bookRepository ) . execute ( new BookId ( command . isbn ) ) ; if ( isDuplicateISBN ) { throw new Error ( '既に存在する書籍です' ) ; } const book = Book . create ( new BookId ( command . isbn ) , new Title ( command . title ) , new Price ( { amount : command . priceAmount , currency : 'JPY' } ) ) ; await this . bookRepository . save ( book ) ; } ) ; } } RegisterBookApplicationService では、まず ISBNDuplicationCheckDomainService を利用して、ISBN の重複チェックを行います。重複している場合は、 Error をスローします。重複していない場合は、 Book エンティティを生成し、 BookRepository を利用して永続化します。 ここで重要なのは、ISBN の重複チェックのビジネスロジックや、 Book エンティティ生成時のビジネスロジックがドメインオブジェクトに隠蔽されているということです。これにより、アプリケーションサービスの実装はドメイン知識を持たない状態で、ドメインオブジェクトを利用するだけでユースケースを実現することができます。 書籍登録アプリケーションサービスのテスト RegisterBookApplicationService ではトランザクション管理オブジェクト transactionManager を DI しています。今回はトランザクションを考慮したテストを行わないため、テスト用にモックを作成しましょう。 Application/shared/ 配下に MockTransactionManager.ts ファイルを作成し、以下のように実装します。 .../Application/shared/MockTransactionManager.ts export class MockTransactionManager { async begin < T > ( callback : ( ) => Promise < T > ) { return await callback ( ) ; } } MockTransactionManager は、 begin メソッドを実装していますが、引数に渡されたコールバック関数をそのまま実行しています。これにより、トランザクションを考慮したテストを行いたくないケースでは、 MockTransactionManager を利用することでテストを行うことができます。 それでは、テストを実装していきましょう。 RegisterBookApplicationService.ts と同じディレクトリ内に RegisterBookApplicationService.test.ts ファイルを作成し、以下のように実装します。 .../Application/Book/RegisterBookApplicationService/RegisterBookApplicationService.test.ts import { InMemoryBookRepository } from 'Infrastructure/InMemory/Book/InMemoryBookRepository' ; import { RegisterBookApplicationService , RegisterBookCommand , } from './RegisterBookApplicationService' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { MockTransactionManager } from 'Application/shared/MockTransactionManager' ; import { bookTestDataCreator } from 'Infrastructure/shared/Book/bookTestDataCreator' ; describe ( 'RegisterBookApplicationService' , ( ) => { it ( '重複書籍が存在しない場合書籍が正常に作成できる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const mockTransactionManager = new MockTransactionManager ( ) ; const registerBookApplicationService = new RegisterBookApplicationService ( repository , mockTransactionManager ) ; const command : Required < RegisterBookCommand > = { isbn : '9784167158057' , title : '吾輩は猫である' , priceAmount : 770 , } ; await registerBookApplicationService . execute ( command ) ; const createdBook = await repository . find ( new BookId ( command . isbn ) ) ; expect ( createdBook ) . not . toBeNull ( ) ; } ) ; it ( '重複書籍が存在する場合エラーを投げる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const mockTransactionManager = new MockTransactionManager ( ) ; const registerBookApplicationService = new RegisterBookApplicationService ( repository , mockTransactionManager ) ; // 重複させるデータを準備 const bookID = '9784167158057' ; await bookTestDataCreator ( repository ) ( { bookId : bookID , } ) ; const command : Required < RegisterBookCommand > = { isbn : bookID , title : '吾輩は猫である' , priceAmount : 770 , } ; await expect ( registerBookApplicationService . execute ( command ) ) . rejects . toThrow ( ) ; } ) ; } ) ; このテストでは、テスト用のリポジトリとトランザクション管理オブジェクト MockTransactionManager を利用し、 RegisterBookApplicationService をインスタンス化し、実行して書籍が正常に作成できることを確認しています。また、重複書籍が存在する場合はエラーが投げられることを確認しています。 アプリケーションサービスのテストでは、値に関する検証を行っていません。その理由は、値に対する検証はドメインオブジェクトのテストですでに行っているからです。このアプローチにより、アプリケーションサービスのテストはユースケースに集中することができます。その結果テストは、よりシンプルで理解しやすくなります。 ! テストで利用するパラメータに TypeScript の Utility Types である Required を利用している点に注目してください。これは、 RegisterBookCommand のプロパティをすべて必須にするために利用しています。 const command : Required < RegisterBookCommand > = { isbn : '9784167158057' , title : '吾輩は猫である' , priceAmount : 770 , } ; コマンド系アプリケーションサービスが実行時に受け取るパラメーターは、ビジネスルールの変更により任意のプロパティが追加される可能性があります。任意のプロパティの追加はテストファイルでコンパイルエラーが出ず、 悪い意味でテストに影響を与えることはありません 。 // application service export type RegisterBookCommand = { isbn : string , title : string , priceAmount : number , author ? : string , // 任意のプロパティの追加 } ; // test // テストでは、実装側で任意のプロパティが追加されてもコンパイルエラーが出ない const command : RegisterBookCommand = { isbn : '9784167158057' , title : '吾輩は猫である' , priceAmount : 770 , } ; そこで、 RegisterBookCommand のプロパティをすべて必須にするために Required を利用しています。これにより、新しいプロパティの追加やドメインモデルの変更が行われた際、既存のテストケースがコンパイルエラーを引き起こすことで、開発者はこれらの変更をテストケースに組み込む必要性に気づきます。これにより、新しいプロパティや変更されたビジネスロジックが適切にテストされることが保証され、テストが常に最新の実装を正確に反映するようになります。結果として、ソフトウェアの品質が継続的に向上します。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest RegisterBookApplicationService.test.ts エラーなく成功すれば OK です。 書籍取得アプリケーションサービスの実装 次は、「書籍を取得する」ユースケースを実現するアプリケーションサービスを実装していきましょう。 Application/Book/GetBookApplicationService ディレクトリを作成ます。次に、 GetBookApplicationService.ts ファイルを作成し、以下のように実装します。 .../Application/Book/GetBookApplicationService/GetBookApplicationService.ts import { Book } from 'Domain/models/Book/Book' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { IBookRepository } from 'Domain/models/Book/IBookRepository' ; export class GetBookApplicationService { constructor ( private bookRepository : IBookRepository ) { } async execute ( isbn : string ) : Promise < Book | null > { const book = await this . bookRepository . find ( new BookId ( isbn ) ) ; return book } } GetBookApplicationService では、 BookRepository を利用して Book エンティティを取得し、 Book エンティティをそのまま返却しています。 この実装には問題があります。ドメインオブジェクトのクライアントはアプリケーションサービスです。現在の実装では Book 集約をそのまま return しています。これでは、アプリケーションサービスのクライアントであるプレゼンテーション層にドメインオブジェクトが漏れてしまい、プレゼンテーション層でドメインオブジェクトを操作できてしまいます。ドメインオブジェクトに依存するレイヤーが増えると、ドメインオブジェクトの変更により、影響範囲が広がり、変更容易性が低下します。 この問題に DTO (Data Transfer Object) を利用することで対応します。 DTO (Data Transfer Object) DTO とはデザインパターンの一つで、一般的には関連データをまとめて転送するためのデータ構造です。ドメイン駆動設計においてはドメインオブジェクトのデータのみをプレゼンテーション層に渡すためのデータ構造と定義することができます。 まずは DTO を作成していきましょう。 Application/Book/ 配下に BookDTO.ts ファイルを作成し、以下のように実装します。 .../Application/Book/BookDTO.ts import { Book } from 'Domain/models/Book/Book' ; import { StatusLabel } from 'Domain/models/Book/Stock/Status/Status' ; export class BookDTO { public readonly bookId : string ; public readonly title : string ; public readonly price : number ; public readonly stockId : string ; public readonly quantityAvailable : number ; public readonly status : StatusLabel ; constructor ( book : Book ) { this . bookId = book . bookId . value ; this . title = book . title . value ; this . price = book . price . value . amount ; this . stockId = book . stockId . value ; this . quantityAvailable = book . quantityAvailable . value ; this . status = book . status . toLabel ( ) ; } } 実装自体は非常にシンプルで、 Book エンティティを受け取り、プロパティに値をセットします。これはただのデータを保持するためのクラスです。しかし、このクラスには重要な役割があります。それは、ドメインオブジェクトをプレゼンテーション層に渡すためのデータ構造としての役割です。このクラスを利用することで、プレゼンテーション層はドメインオブジェクトを知ることなく、データのみを利用させるように制御することができます。 BookDTO はデータ取得系のアプリケーションサービスで使い回すことができます。 GetBookApplicationService を修正し、 BookDTO を返却するように実装しましょう。 .../Application/Book/GetBookApplicationService/GetBookApplicationService.ts async execute ( isbn : string ) : Promise < BookDTO | null > { const book = await this . bookRepository . find ( new BookId ( isbn ) ) ; return book ? new BookDTO ( book ) : null ; } これで、 GetBookApplicationService は BookDTO を返却するようになり、ドメインオブジェクトがアプリケーション層から漏れ出すことを防ぐことができます。 書籍取得アプリケーションサービスのテスト それでは、テストを実装していきましょう。 GetBookApplicationService と同じディレクトリ内に GetBookApplicationService.test.ts ファイルを作成し、以下のように実装します。 .../Application/Book/GetBookApplicationService/GetBookApplicationService.test.ts import { InMemoryBookRepository } from 'Infrastructure/InMemory/Book/InMemoryBookRepository' ; import { GetBookApplicationService } from './GetBookApplicationService' ; import { bookTestDataCreator } from 'Infrastructure/shared/Book/bookTestDataCreator' ; import { BookDTO } from '../BookDTO' ; describe ( 'GetBookApplicationService' , ( ) => { it ( '指定されたIDの書籍が存在する場合、DTOに詰め替えられ、取得できる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const getBookApplicationService = new GetBookApplicationService ( repository ) ; // テスト用データ作成 const createdBook = await bookTestDataCreator ( repository ) ( { } ) ; const data = await getBookApplicationService . execute ( createdBook . bookId . value ) ; expect ( data ) . toEqual ( new BookDTO ( createdBook ) ) ; } ) ; it ( '指定されたIDの書籍が存在しない場合、nullが取得できる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const getBookApplicationService = new GetBookApplicationService ( repository ) ; const data = await getBookApplicationService . execute ( '9784167158057' ) ; expect ( data ) . toBeNull ( ) ; } ) ; } ) ; このテストでは、 GetBookApplicationService を利用して、指定された ID の書籍が存在する場合は BookDTO に詰め替えられ、取得できることを確認しています。また、指定された ID の書籍が存在しない場合は null が取得できることを確認しています。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest GetBookApplicationService.test.ts エラーなく成功すれば OK です。 在庫追加アプリケーションサービスの実装 次は、「在庫を追加する」ユースケースを実現するアプリケーションサービスを実装していきましょう。 Application/Book/IncreaseBookStockApplicationService ディレクトリを作成します。次に、 IncreaseBookStockApplicationService.ts ファイルを作成し、以下のように実装します。 .../Application/Book/IncreaseBookStockApplicationService/IncreaseBookStockApplicationService.ts import { ITransactionManager } from 'Application/shared/ITransactionManager' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { IBookRepository } from 'Domain/models/Book/IBookRepository' ; export type IncreaseBookStockCommand = { bookId : string ; incrementAmount : number ; } ; export class IncreaseBookStockApplicationService { constructor ( private bookRepository : IBookRepository , private transactionManager : ITransactionManager ) { } async execute ( command : IncreaseBookStockCommand ) : Promise < void > { await this . transactionManager . begin ( async ( ) => { const book = await this . bookRepository . find ( new BookId ( command . bookId ) ) ; if ( ! book ) { throw new Error ( '書籍が存在しません' ) ; } book . increaseStock ( command . incrementAmount ) ; await this . bookRepository . update ( book ) ; } ) ; } } IncreaseBookStockApplicationService では、 BookRepository を利用して Book エンティティを一度取得します。その後、 Book エンティティの increaseStock メソッドを利用して在庫を増やします。最後に、 BookRepository を利用して永続化します。 更新系のアプリケーションサービスで特徴的なのが、一度リポジトリから Book エンティティを取得し、その後に increaseStock メソッドを利用して在庫を増やしてる点です。これは、 Book エンティティの increaseStock メソッドが、在庫を増やすために必要なビジネスロジックを持っているためです。このような実装にすることで、在庫の増減に関するドメイン知識がドメイン層に集約され、アプリケーションサービスの実装はドメイン知識を持たない状態で、ドメインオブジェクトを利用するだけでユースケースを 安全に 実現することができます。 在庫追加アプリケーションサービスのテスト それでは、テストを実装していきましょう。 IncreaseBookStockApplicationService と同じディレクトリ内に IncreaseBookStockApplicationService.test.ts ファイルを作成し、以下のように実装します。 .../Application/Book/IncreaseBookStockApplicationService/IncreaseBookStockApplicationService.test.ts import { InMemoryBookRepository } from 'Infrastructure/InMemory/Book/InMemoryBookRepository' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { MockTransactionManager } from 'Application/shared/MockTransactionManager' ; import { bookTestDataCreator } from 'Infrastructure/shared/Book/bookTestDataCreator' ; import { IncreaseBookStockApplicationService , IncreaseBookStockCommand , } from './IncreaseBookStockApplicationService' ; describe ( 'IncreaseBookStockApplicationService' , ( ) => { it ( '書籍の在庫を増加することができる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const mockTransactionManager = new MockTransactionManager ( ) ; const increaseBookStockApplicationService = new IncreaseBookStockApplicationService ( repository , mockTransactionManager ) ; // テスト用データ準備 const bookId = '9784167158057' ; await bookTestDataCreator ( repository ) ( { bookId , quantityAvailable : 0 , } ) ; const incrementAmount = 100 ; const command : Required < IncreaseBookStockCommand > = { bookId , incrementAmount , } ; await increaseBookStockApplicationService . execute ( command ) ; const updatedBook = await repository . find ( new BookId ( bookId ) ) ; expect ( updatedBook ?. quantityAvailable . value ) . toBe ( incrementAmount ) ; } ) ; it ( '書籍が存在しない場合エラーを投げる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const mockTransactionManager = new MockTransactionManager ( ) ; const increaseBookStockApplicationService = new IncreaseBookStockApplicationService ( repository , mockTransactionManager ) ; const bookId = '9784167158057' ; const incrementAmount = 100 ; const command : Required < IncreaseBookStockCommand > = { bookId , incrementAmount , } ; await expect ( increaseBookStockApplicationService . execute ( command ) ) . rejects . toThrow ( ) ; } ) ; } ) ; このテストでは、 IncreaseBookStockApplicationService を利用して、書籍の在庫を増加することができることを確認しています。また、更新対象の書籍が存在しない場合はエラーを投げることを確認しています。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest IncreaseBookStockApplicationService.test.ts エラーなく成功すれば OK です。 書籍削除アプリケーションサービスの実装 最後は、「書籍を削除する」ユースケースを実現するアプリケーションサービスを実装していきましょう。 Application/Book/DeleteBookApplicationService ディレクトリを作成します。次に、 DeleteBookApplicationService.ts ファイルを作成し、以下のように実装します。 .../Application/Book/DeleteBookApplicationService/DeleteBookApplicationService.ts import { ITransactionManager } from 'Application/shared/ITransactionManager' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { IBookRepository } from 'Domain/models/Book/IBookRepository' ; export type DeleteBookCommand = { bookId : string ; } ; export class DeleteBookApplicationService { constructor ( private bookRepository : IBookRepository , private transactionManager : ITransactionManager ) { } async execute ( command : DeleteBookCommand ) : Promise < void > { await this . transactionManager . begin ( async ( ) => { const book = await this . bookRepository . find ( new BookId ( command . bookId ) ) ; if ( ! book ) { throw new Error ( '書籍が存在しません' ) ; } book . delete ( ) ; await this . bookRepository . delete ( book . bookId ) ; } ) ; } } DeleteBookApplicationService では、 BookRepository を利用して Book エンティティを一度取得します。その後、 Book エンティティの delete メソッドを利用して書籍削除時の処理を行います。ここでは、書籍削除可能かどうかの判定を Book エンティティの delete メソッド内で行っています。最後に、 BookRepository を利用して永続化します。 書籍削除アプリケーションサービスのテスト それでは、テストを実装していきましょう。 DeleteBookApplicationService と同じディレクトリ内に DeleteBookApplicationService.test.ts ファイルを作成し、以下のように実装します。 .../Application/Book/DeleteBookApplicationService/DeleteBookApplicationService.test.ts import { InMemoryBookRepository } from 'Infrastructure/InMemory/Book/InMemoryBookRepository' ; import { BookId } from 'Domain/models/Book/BookId/BookId' ; import { MockTransactionManager } from 'Application/shared/MockTransactionManager' ; import { bookTestDataCreator } from 'Infrastructure/shared/Book/bookTestDataCreator' ; import { DeleteBookApplicationService , DeleteBookCommand , } from './DeleteBookApplicationService' ; describe ( 'DeleteBookApplicationService' , ( ) => { it ( '書籍を削除することができる' , async ( ) => { const repository = new InMemoryBookRepository ( ) ; const mockTransactionManager = new MockTransactionManager ( ) ; const deleteBookApplicationService = new DeleteBookApplicationService ( repository , mockTransactionManager ) ; // テスト用データ作成 const bookId = '9784167158057' ; await bookTestDataCreator ( repository ) ( { bookId , } ) ; const command : Required < DeleteBookCommand > = { bookId } ; await deleteBookApplicationService . execute ( command ) ; const deletedBook = await repository . find ( new BookId ( bookId ) ) ; expect ( deletedBook ) . toBe ( null ) ; } ) ; } ) ; このテストでは、 DeleteBookApplicationService を利用して、書籍を削除することができることを確認しています。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest DeleteBookApplicationService.test.ts 以上で、CRUD 操作を実現するアプリケーションサービスの実装は完了です。 基本的なユースケースはこれらの実装をベースに表現することが可能です。アプリケーションサービスの実装で重要なのは、ドメイン知識をドメインオブジェクト内に閉じ込め、ドメインオブジェクトを利用してユースケースを実現することです。アプリケーションサービス内にドメイン知識が漏れていないかを常に意識して実装するようにしましょう。 まとめ アプリケーションサービスは、ユースケースを実現するための操作を提供するサービス アプリケーションサービスは、ドメインオブジェクトを利用してユースケースを実現する 本章では、アプリケーションサービスの説明と実装を行いました。 次章では、プレゼンテーション層の実装を行います。API を利用してアプリケーションサービスを呼び出し、システムを構築していきましょう。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/application-service PREV リポジトリ (Repository) NEXT プレゼンテーション (Presentation) GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter8_value_object
================================================================================

このチャプターの目次 値オブジェクトとは 値とは 値の特徴 値オブジェクトの実装 実装 ビジネスルールの適用 値オブジェクトのテスト プリミティブな値を値オブジェクトにする基準 値オブジェクトのリファクタリング すべての値オブジェクトの実装 まとめ これまでのコード 参考文献 値オブジェクトとは 値オブジェクト (Value Object) とは、エンティティと並びドメインモデル (ドメインオブジェクト) の中心的な要素で、ドメイン内のさまざまな 値 の概念をモデル化するのに用いられます。値には、名前、年齢、色などがあります。値オブジェクトは、これらの値を表現するために使用されます。 値とは ここで少し値とは何かを考えてみましょう。これは 「xxxxxxxx」 という値で BookId という変数に代入されています。このコードは正しいように見えます。当然 TypeScript では正しい構文ですし、エラーも発生しません。では、変数 BookId の値として「xxxxxxxx」は正しいでしょうか？ const BookId : string = 'xxxxxxxx' ; ドメインモデリングで作成した BookId.pu を振り返ってみましょう。確認したところ BookId として正しい値は ISBN コード であることが読み取れます。ISBN コードはいくつものルールの組み合わせによって構成されています。 StockManagement/Domain/models/Book/BookId/BookId.pu @startuml BookId class 'BookId' as BookId { + value : string } note bottom of BookId
    ISBNコードを適用する。
    ISBNコードは、ISBNのあとに数字で「978」、
    さらにグループ（国・地域）番号（日本は4）、出版社番号、書名番号、の合計12桁の数字を並べ、
    最後にこの12桁の数字を特定の計算式で演算して得た1桁のチェック用の数を付け加えたコード。 end note @enduml BookId.pu の内容に従い値を ISBN コードに修正しました。これで変数 BookId を正しい値にすることができました。 const BookId : string = '9774167158057' ; と思いましたが、ISBN コードは「978」から始まらなければいけないところを間違えて「977」から始めてしまいました。正しくは '9784167158057' です。さて、このミスに気づけた方はいるでしょうか？ また、この数字の羅列を見て ISBN コードであると理解できる方がどれほどいるでしょうか？つまり BookId は不正な状態で存在することが可能であり、正しい値が何かわからないという状況です。これではバグの温床になってしまいます。 このような問題を値オブジェクトは解決します。 値の特徴 値オブジェクトを実装する前に、値の特徴を抑えておきましょう。値には主に 3 つの特徴があります。そしてこれらは値オブジェクトに対しても同様に当てめる必要があります。 不変に保つことができる 値同士が等しいか比較できる 副作用がない ではそれぞれ確認していきましょう。 不変に保つことができる 私たちが頻繁に使う値にはプリミティブ型（文字列、数値、ブーリアンなど）があります。これらの値は不変です。不変とは、その 値自体 を変更することができないという意味です。たとえば「あ」という値を「い」に変更することはできません。 // 'あ' という値自体を変更することはできません。 'あ' = 'い' ; この例では、 'あ' という値自体を変更しようとしていますが、これはエラーになります。このように、プリミティブな値は不変であるため、その値自体を変更することはできません。もしそんなことが可能であれば、それまで「あ」として扱っていたものを「い」として扱わなければならなくなります。意味が分かりませんね。 値同士が等しいか比較できる 「値同士が等しいか比較できる」という特徴は直感的です。プリミティブな値は、その内容によって等価性が判断されます。 console . log ( 'Hello' === 'Hello' ) ; // true console . log ( 'Hello' === 'Goodbye' ) ; // false この例では、最初の比較では二つの値「Hello」と「Hello」が完全に同じであるため、結果は true になります。これは、プリミティブ型の値がその値によって等価性が判断されることを示しています。一方で、二つ目の比較では、二つの異なる値「Hello」と「Goodbye」を比較しているため、結果は false になります。このように、プリミティブ型の値同士を比較する場合、その値が直接比較されます。 副作用がない 「副作用がない」とはどういうことでしょうか？プログラミングにおいて、「副作用がない」とは、ある操作が他の状態に予期せぬ影響を与えないことを意味します。そして値の操作には副作用がありません。たとえばプリミティブな値である文字列は、 String.prototype から toUpperCase() 、 toLowerCase() などのメソッドを継承していますが、これらのメソッドには副作用がありません。 // toUpperCaseメソッドを呼び出す console . log ( 'Hello' . toUpperCase ( ) ) ; // "HELLO" console . log ( 'Hello' ) ; // "Hello" (値自体が変更されていない) この例では、 toUpperCase() メソッドを呼び出していますが、このメソッドは文字列を大文字に変換するだけで、値自体を変更することはありません。そのため、 toUpperCase() メソッドを呼び出したあとでも、元の値は変更されていません。これは、値の操作には副作用がないことを示しています。 値オブジェクトの実装 値の特徴が確認できたので、実際に値オブジェクトを実装してみましょう。 実装 まずは、 必要なパッケージをインストールしましょう。 StockManagement/ $ npm i lodash nanoid@3 #バージョンはは3系を指定してください $ npm i -D @types/lodash src/Domain/models/Book/BookId/ 配下に BookId.ts を作成し以下のように実装します。これが基本的な値オブジェクトのベースになります。 src/Domain/models/Book/BookId/BookId.ts import isEqual from 'lodash/isEqual' ; export class BookId { private readonly _value : string ; constructor ( value : string ) { this . _value = value ; } equals ( other : BookId ) : boolean { return isEqual ( this . _value , other . _value ) ; } get value ( ) : string { return this . _value ; } } ! 値オブジェクトは、プリミティブな値だけでなく、複雑なデータ構造を持つ場合があります。 lodash の isEqual は、オブジェクトや配列などの複雑なデータ構造を持つ値に対しても、深い比較 (Deep Comparison) を実行できます。しかし大量のデータを扱うシステムでは、パフォーマンスに影響する可能性があるため、要件によって取捨選択してください。 それでは BookId 値オブジェクトを用いて、値オブジェクトの特徴がどのように実装されているかを確認してみましょう。 不変に保つことができる 値オブジェクト自体を変更することはできません。また、 BookId 値オブジェクトの value プロパティは private readonly で定義されており、一度設定された後は変更できません。 // 値オブジェクト自体を変更することはできません。 new BookId ( '9784167158057' ) = new BookId ( '9784167158057' ) // valueプロパティはprivate readonlyで定義されているため、一度設定された後は変更できません。 new BookId ( '9784167158057' ) . value = '9784167158057' // エラー 値同士が等しいか比較できる equals メソッドを用いて、他の BookId インスタンスとの等価性を比較できます。 console . log ( new BookId ( '9784167158057' ) . equals ( new BookId ( '9784167158057' ) ) ) ; // true console . log ( new BookId ( '9784167158057' ) . equals ( new BookId ( '9780306406157' ) ) ) ; // false ! 値オブジェクト同士の比較には必ず equals メソッドを利用しましょう。 値オブジェクトは値です 。値の比較は値同士を比較することで行いました。以下のコードは '9784167158057'.value === '9784167158057'.value と同じで、値の値 (value) を取り出して比較を行なっており値の比較として不自然な実装です。 console . log ( new BookId ( '9784167158057' ) . value === new BookId ( '9784167158057' ) . value ) ; // NG 副作用がない BookId 値オブジェクト内で行われる操作は、他のオブジェクトや外部状態に影響を与えるような副作用を持ちません。たとえば、 equals メソッドは比較のために内部状態を変更することはなく、単純な値の比較のみを行います。 // equalsメソッドを呼び出す new BookId ( '9784167158057' ) . equals ( new BookId ( '9784167158057' ) ) ; console . log ( new BookId ( '9784167158057' ) . value ) ; // "9784167158057" (値自体が変更されていない) ビジネスルールの適用 実装した値オブジェクトが値の特徴を満たしていることが確認できました。ですが、まだ未完成です。今のままでは変わらず不正な ISBN で BookId を生成することが可能です。値オブジェクトの真髄は値にビジネスルールを適用できる点にあります。それでは、ビジネスルールを適用していきましょう。(バリデーション、変換のロジックの実装は省略します) src/Domain/models/Book/BookId/BookId.ts import { isEqual } from 'lodash' ; export class BookId { private readonly _value : string ; static MAX_LENGTH = 13 ; static MIN_LENGTH = 10 ; constructor ( value : string ) { this . validate ( value ) ; this . _value = value ; } private validate ( isbn : string ) : void { if ( isbn . length < BookId . MIN_LENGTH || isbn . length > BookId . MAX_LENGTH ) { throw new Error ( 'ISBNの文字数が不正です' ) ; } if ( ! this . isValidIsbn10 ( isbn ) && ! this . isValidIsbn13 ( isbn ) ) { throw new Error ( '不正なISBNの形式です' ) ; } } private isValidIsbn10 ( isbn10 : string ) : boolean { // ISBN-10 のバリデーションロジックを実装 // 実際の実装ではここにチェックディジットを計算するロジックが必要です。 return isbn10 . length === 10 ; // ここを実際のチェックディジット計算に置き換える } private isValidIsbn13 ( isbn13 : string ) : boolean { // ISBN-13 のバリデーションロジックを実装 // ここでは簡単な例を示しますが、実際にはより複雑なチェックが必要です return isbn13 . startsWith ( '978' ) && isbn13 . length === 13 ; } equals ( other : BookId ) : boolean { return isEqual ( this . _value , other . _value ) ; } get value ( ) : string { return this . _value ; } toISBN ( ) : string { if ( this . _value . length === 10 ) { // ISBNが10桁の場合の、'ISBN' フォーマットに変換します。 const groupIdentifier = this . _value . substring ( 0 , 1 ) ; // 国コードなど（1桁） const publisherCode = this . _value . substring ( 1 , 3 ) ; // 出版者コード（2桁） const bookCode = this . _value . substring ( 3 , 9 ) ; // 書籍コード（6桁） const checksum = this . _value . substring ( 9 ) ; // チェックディジット（1桁） return ` ISBN ${ groupIdentifier } - ${ publisherCode } - ${ bookCode } - ${ checksum } ` ; } else { // ISBNが13桁の場合の、'ISBN' フォーマットに変換します。 const isbnPrefix = this . _value . substring ( 0 , 3 ) ; // 最初の3桁 (978 または 979) const groupIdentifier = this . _value . substring ( 3 , 4 ) ; // 国コードなど（1桁） const publisherCode = this . _value . substring ( 4 , 6 ) ; // 出版者コード（2桁） const bookCode = this . _value . substring ( 6 , 12 ) ; // 書籍コード（6桁） const checksum = this . _value . substring ( 12 ) ; // チェックディジット（1桁） return ` ISBN ${ isbnPrefix } - ${ groupIdentifier } - ${ publisherCode } - ${ bookCode } - ${ checksum } ` ; } } } まずコンストラクタでは validate メソッドを使って入力された ISBN をバリデーションします。これにより BookId は無効な形式の ISBN を受け付けず、適切なフォーマットの ISBN だけ が BookId 値オブジェクトとして作成されます。これにより、不正なデータがシステムに流入するリスクが軽減されます。 constructor ( value : string ) { this . validate ( value ) ; this . _value = value ; } ! バリデーションでは、構文チェック等の前に文字数のチェックを行うことを推奨します。なぜなら仮に文字列が 10 億桁だった場合、正規表現エンジンが読み込み負荷の重い処理を無駄に実行してしまい、パフォーマンスに影響が出る可能性があります。 また、文字数のチェックを先に行うことで、正規表現のパターンを簡単にすることができます。 private validate ( isbn : string ) : void { if ( isbn . length < BookId . MIN_LENGTH || isbn . length > BookId . MAX_LENGTH ) { throw new Error ( 'ISBNの文字数が不正です' ) ; } ( 省略 ) } toISBN メソッドでは BookId の値から ISBN コードの表示用フォーマットへの変換を行なっています。これにより、ISBN フォーマットへの変換ロジックを 値自身で管理 (カプセル化) することができ、保守性が向上します。 toISBN ( ) : string { if ( this . _value . length === 10 ) { // ISBNが10桁の場合の、'ISBN' フォーマットに変換します。 const groupIdentifier = this . _value . substring ( 0 , 1 ) ; // 国コードなど（1桁） const publisherCode = this . _value . substring ( 1 , 3 ) ; // 出版者コード（2桁） const bookCode = this . _value . substring ( 3 , 9 ) ; // 書籍コード（6桁） const checksum = this . _value . substring ( 9 ) ; // チェックディジット（1桁） return ` ISBN ${ groupIdentifier } - ${ publisherCode } - ${ bookCode } - ${ checksum } ` ; } else { // ISBNが13桁の場合の、'ISBN' フォーマットに変換します。 const isbnPrefix = this . _value . substring ( 0 , 3 ) ; // 最初の3桁 (978 または 979) const groupIdentifier = this . _value . substring ( 3 , 4 ) ; // 国コードなど（1桁） const publisherCode = this . _value . substring ( 4 , 6 ) ; // 出版者コード（2桁） const bookCode = this . _value . substring ( 6 , 12 ) ; // 書籍コード（6桁） const checksum = this . _value . substring ( 12 ) ; // チェックディジット（1桁） return ` ISBN ${ isbnPrefix } - ${ groupIdentifier } - ${ publisherCode } - ${ bookCode } - ${ checksum } ` ; } } これらの実装により、値にビジネスルールを適用することができました。 値オブジェクトのテスト 値オブジェクトのビジネスルールが正しく実装されているかを保証するためにはテストは必須です。DDD においてビジネス環境や市場の変化、組織の戦略の変更などにより、ビジネスルールも変わる可能性は十分にあります。テストを書いておくことで、値オブジェクトのビジネスルールを正しく理解し、その振る舞いが変更された場合にもすぐに気づくことができます。 それではテストを書いていきましょう。 BookId.ts と同じディレクトリに BookId.test.ts を作成し、以下のように実装します。 src/Domain/models/Book/BookId/BookId.test.ts import { BookId } from './BookId' ; describe ( 'BookId' , ( ) => { // 正常系 test ( '有効なフォーマットの場合正しい変換結果を期待' , ( ) => { expect ( new BookId ( '9784167158057' ) . value ) . toBe ( '9784167158057' ) ; expect ( new BookId ( '4167158051' ) . value ) . toBe ( '4167158051' ) ; } ) ; test ( 'equals' , ( ) => { const bookId1 = new BookId ( '9784167158057' ) ; const bookId2 = new BookId ( '9784167158057' ) ; const bookId3 = new BookId ( '9781234567890' ) ; expect ( bookId1 . equals ( bookId2 ) ) . toBeTruthy ( ) ; expect ( bookId1 . equals ( bookId3 ) ) . toBeFalsy ( ) ; } ) ; test ( 'toISBN() 13桁' , ( ) => { const bookId = new BookId ( '9784167158057' ) ; expect ( bookId . toISBN ( ) ) . toBe ( 'ISBN978-4-16-715805-7' ) ; } ) ; test ( 'toISBN() 10桁' , ( ) => { const bookId = new BookId ( '4167158051' ) ; expect ( bookId . toISBN ( ) ) . toBe ( 'ISBN4-16-715805-1' ) ; } ) ; // 異常系 test ( '不正な文字数の場合にエラーを投げる' , ( ) => { // 境界値のテスト expect ( ( ) => new BookId ( '1' . repeat ( 101 ) ) ) . toThrow ( 'ISBNの文字数が不正です' ) ; expect ( ( ) => new BookId ( '1' . repeat ( 9 ) ) ) . toThrow ( 'ISBNの文字数が不正です' ) ; } ) ; test ( '不正なフォーマットの場合にエラーを投げる' , ( ) => { expect ( ( ) => new BookId ( '9994167158057' ) ) . toThrow ( '不正なISBNの形式です' ) ; } ) ; } ) ; ビジネスロジック、振る舞い (メソッド) 、例外処理などを網羅するようにテストを書きます。 値オブジェクトのテストではなるべくカバレッジが 100%に近づけるようにしましょう。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest
 PASS  src/Domain/models/Book/BookId/BookId.test.ts
  BookId
    ✓ 有効なフォーマットの場合正しい変換結果を期待 ( 11 ms ) ✓ equals ( 1 ms ) ✓ toISBN ( ) 13 桁
    ✓ toISBN ( ) 10 桁
    ✓ 不正な文字数の場合にエラーを投げる ( 46 ms ) ✓ 不正なフォーマットの場合にエラーを投げる ( 4 ms ) エラーなく成功すれば OK です。 以上で BookId 値オブジェクトの実装は完了です。ビジネスロジックを値にカプセル化し、テストによって品質を担保することができました。そして最初に値が抱えていた「 不正な状態で存在することが可能であり、正しい値が何かわからない 」という問題を解決することができました。 プリミティブな値を値オブジェクトにする基準 値オブジェクトの素晴らしさを実感できたと思います。ですが、値オブジェクトの実装にはコストがかかるため、すべての値を値オブジェクトにするかどうかは慎重に判断する必要があります。そこで、値オブジェクトに適した値の特徴を確認しましょう。 意味のある単一の概念を表す値 通貨、距離、時間、範囲など、単一の概念や複数の関連する値を一つの単位として表現したい場合、それらの値の一貫性を保持しやすくなります。例えば、金額と通貨を一緒に扱うことで、通貨の変換や計算の一貫性が保たれます。 ビジネスルールを持つ オブジェクトが独自のバリデーションルールやドメイン特有のビジネスロジック (メールアドレスの形式、電話番号の形式など) を必要とする場合、それらのデータの正確性が保たれます。 再利用性 同じ値がドメイン内の複数の箇所で必要な場合、再利用できることにより開発の効率が向上します。これは新しい機能の追加や、既存機能の拡張が容易になります。 上記の特徴やメリットと、実装コストを比較した上で導入するかどうかを判断しましょう。 ! すべての値を値オブジェクトにするかどうかは慎重に判断する必要があります TypeScript においては、安全性の観点でコストを払ってすべての値を値オブジェクトとして実装するメリットがあると考えます。 TypeScript には 名前付き引数 がないため、プリミティブな型を利用すると以下のような問題に気づくのが難しくなります。 class Person { constructor ( public firstName : string , public lastName : string , public email : string , public phoneNumber : string , ) { } } const person = new Person ( 'John' , 'Doe' , '09012341234' , // emailとphoneNumberの順番を間違えてしまった。が、エラーが出ないため気づけない。 '<EMAIL>' , ) ; すべての値を値オブジェクトとして定義することで、コンパイル時にエラーが出るため、間違いに気づくことができます。 class Person { constructor ( public firstName : FirstName , public lastName : LastName , public email : Email , public phoneNumber : PhoneNumber , ) { } } new Person ( new FirstName ( 'John' ) , new LastName ( 'Doe' ) , new PhoneNumber ( '09012341234' ) , // エラーが出て順序の間違いに気づく new Email ( '<EMAIL>' ) , ) ; 値オブジェクトのリファクタリング 次はリファクタリングを行いましょう。ここでは共通処理を抽象化します。たとえば、値オブジェクトの等価性を比較する equals メソッドは、すべての値オブジェクトで同じ実装を行う必要があります。これは、値オブジェクトの実装を重複させることになります。そこで、値オブジェクトの共通処理を抽象化することで、値オブジェクトの実装を簡潔にできます。 それではすべての値オブジェクトが継承する 共通クラス ValueObject を作成します。 src/Domain/models/shared/ValueObject.ts import { isEqual } from 'lodash' ; export abstract class ValueObject < T , U > { // @ts-expect-error private _type : U ; protected readonly _value : T ; constructor ( value : T ) { this . validate ( value ) ; this . _value = value ; } protected abstract validate ( value : T ) : void ; get value ( ) : T { return this . _value ; } equals ( other : ValueObject < T , U > ) : boolean { return isEqual ( this . _value , other . _value ) ; } } ! どこからも参照されず、利用されることもないメンバ変数 _type が定義されている理由を補足します。 export abstract class ValueObject < T , U > { // @ts-expect-error private _type : U ; ( 省略 ) } TypeScript は 構造的型付け (Structural Typing) を採用しているため、型の互換性はその型が持つ構造 (プロパティやメソッド) に基づいて判断されます。 たとえば、以下のように CustomerId 、 OrderId 2 つのクラスがあるとします。 class CustomerId { constructor ( public readonly id : string ) { } } class OrderId { constructor ( public readonly id : string ) { } } const log = ( customerId : CustomerId ) => { console . log ( customerId . id ) ; } log ( new OrderId ( '1' ) ) ; // OrderIdを渡してもエラーにならない これらは同じ構造を持っているため、 TypeScript はこれらの型を同等とみなします。これは意図しないバグを引き起こす可能性があります。たとえば、 CustomerId を期待する関数に OrderId を渡すことができてしまいます。このような型の混同を防ぐために、 _type のような専用のプロパティを追加します。これにより、構造的には同じでも、このプライベートプロパティのおかげで異なる型として認識させることができます。 次に、 BookId 値オブジェクトを ValueObject を継承するようにリファクタリングします。共通の処理が ValueObject に移動したため、 BookId 値オブジェクトは自身のビジネスロジックのみを実装することができ、簡潔になりました。 src/Domain/models/Book/BookId/BookId.ts import { ValueObject } from 'Domain/models/shared/ValueObject' ; export class BookId extends ValueObject < string , 'BookId' > { static MAX_LENGTH = 13 ; static MIN_LENGTH = 10 ; constructor ( value : string ) { super ( value ) ; } protected validate ( isbn : string ) : void { ( 省略 ) } private isValidIsbn10 ( isbn10 : string ) : boolean { ( 省略 ) } private isValidIsbn13 ( isbn13 : string ) : boolean { ( 省略 ) } toISBN ( ) : string { ( 省略 ) } } リファクタリングを行ったのでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest
 PASS  src/Domain/models/Book/BookId/BookId.test.ts エラーなく成功すれば OK です。 すべての値オブジェクトの実装 それではここまでの知識を用いて、すべての値オブジェクトを実装していきましょう。それぞれの細かい説明はここでは省略しますが、基本的な考え方、実装の流れ、テストの書き方は BookId と同様です。 Price src/Domain/models/Book/Price/Price.ts import { ValueObject } from 'Domain/models/shared/ValueObject' ; interface PriceValue { amount : number ; currency : 'JPY' ; // USD などの通貨を追加する場合はここに追加します } export class Price extends ValueObject < PriceValue , 'Price' > { static readonly MAX = 1000000 ; static readonly MIN = 1 ; constructor ( value : PriceValue ) { super ( value ) ; } protected validate ( value : PriceValue ) : void { if ( value . currency !== 'JPY' ) { throw new Error ( '現在は日本円のみを扱います。' ) ; } if ( value . amount < Price . MIN || value . amount > Price . MAX ) { throw new Error ( ` 価格は ${ Price . MIN } 円から ${ Price . MAX } 円の間でなければなりません。 ` ) ; } } get amount ( ) : PriceValue [ 'amount' ] { return this . value . amount ; } get currency ( ) : PriceValue [ 'currency' ] { return this . value . currency ; } } src/Domain/models/Book/Price/Price.test.ts import { Price } from './Price' ; describe ( 'Price' , ( ) => { // 正常系 it ( '正しい値と通貨コードJPYで有効なPriceを作成する' , ( ) => { const validAmount = 500 ; const price = new Price ( { amount : validAmount , currency : 'JPY' } ) ; expect ( price . amount ) . toBe ( validAmount ) ; expect ( price . currency ) . toBe ( 'JPY' ) ; } ) ; // 異常系 it ( '無効な通貨コードの場合エラーを投げる' , ( ) => { const invalidCurrency = 'USD' ; expect ( ( ) => { // @ts-expect-error テストのために無効な値を渡す new Price ( { amount : 500 , currency : invalidCurrency } ) ; } ) . toThrow ( '現在は日本円のみを扱います。' ) ; } ) ; it ( 'MIN未満の値でPriceを生成するとエラーを投げる' , ( ) => { const lessThanMin = Price . MIN - 1 ; expect ( ( ) => { new Price ( { amount : lessThanMin , currency : 'JPY' } ) ; } ) . toThrow ( ` 価格は ${ Price . MIN } 円から ${ Price . MAX } 円の間でなければなりません。 ` ) ; } ) ; it ( 'MAX超の値でPriceを生成するとエラーを投げる' , ( ) => { const moreThanMax = Price . MAX + 1 ; expect ( ( ) => { new Price ( { amount : moreThanMax , currency : 'JPY' } ) ; } ) . toThrow ( ` 価格は ${ Price . MIN } 円から ${ Price . MAX } 円の間でなければなりません。 ` ) ; } ) ; } ) ; Title src/Domain/models/Book/Title/Title.ts import { ValueObject } from 'Domain/models/shared/ValueObject' ; type TitleValue = string ; export class Title extends ValueObject < TitleValue , 'Title' > { static readonly MAX_LENGTH = 1000 ; static readonly MIN_LENGTH = 1 ; constructor ( value : TitleValue ) { super ( value ) ; } protected validate ( value : TitleValue ) : void { if ( value . length < Title . MIN_LENGTH || value . length > Title . MAX_LENGTH ) { throw new Error ( ` タイトルは ${ Title . MIN_LENGTH } 文字以上、 ${ Title . MAX_LENGTH } 文字以下でなければなりません。 ` ) ; } } } src/Domain/models/Book/Title/Title.test.ts import { Title } from './Title' ; describe ( 'Title' , ( ) => { test ( 'Titleが1文字で作成できる' , ( ) => { expect ( new Title ( 'a' ) . value ) . toBe ( 'a' ) ; } ) ; test ( 'Titleが1000文字で作成できる' , ( ) => { const longTitle = 'a' . repeat ( 1000 ) ; expect ( new Title ( longTitle ) . value ) . toBe ( longTitle ) ; } ) ; test ( '最小長以上の値でTitleを生成するとエラーを投げる' , ( ) => { expect ( ( ) => new Title ( '' ) ) . toThrow ( 'タイトルは1文字以上、1000文字以下でなければなりません。' ) ; } ) ; test ( '最大長以上の値でTitleを生成するとエラーを投げる' , ( ) => { const tooLongTitle = 'a' . repeat ( 1001 ) ; expect ( ( ) => new Title ( tooLongTitle ) ) . toThrow ( 'タイトルは1文字以上、1000文字以下でなければなりません。' ) ; } ) ; } ) ; StockId src/Domain/models/Book/Stock/StockId/StockId.ts import { ValueObject } from 'Domain/models/shared/ValueObject' ; import { nanoid } from 'nanoid' ; type StockIdValue = string ; export class StockId extends ValueObject < StockIdValue , 'StockId' > { static readonly MAX_LENGTH = 100 ; static readonly MIN_LENGTH = 1 ; constructor ( value : StockIdValue = nanoid ( ) ) { // デフォルトではnanoidを利用しID生成 super ( value ) ; } protected validate ( value : StockIdValue ) : void { if ( value . length < StockId . MIN_LENGTH || value . length > StockId . MAX_LENGTH ) { throw new Error ( ` StockIdは ${ StockId . MIN_LENGTH } 文字以上、 ${ StockId . MAX_LENGTH } 文字以下でなければなりません。 ` ) ; } } } src/Domain/models/Book/Stock/StockId/StockId.test.ts import { StockId } from './StockId' ; // nanoid() をモックする jest . mock ( 'nanoid' , ( ) => ( { nanoid : ( ) => 'testIdWithExactLength' , } ) ) ; describe ( 'StockId' , ( ) => { test ( 'デフォルトの値でStockIdを生成する' , ( ) => { const stockId = new StockId ( ) ; expect ( stockId . value ) . toBe ( 'testIdWithExactLength' ) ; } ) ; test ( '指定された値でStockIdを生成する' , ( ) => { const value = 'customId' ; const stockId = new StockId ( value ) ; expect ( stockId . value ) . toBe ( value ) ; } ) ; test ( '最小長以下の値でStockIdを生成するとエラーを投げる' , ( ) => { const shortValue = '' ; expect ( ( ) => new StockId ( shortValue ) ) . toThrowError ( new Error ( ` StockIdは ${ StockId . MIN_LENGTH } 文字以上、 ${ StockId . MAX_LENGTH } 文字以下でなければなりません。 ` ) ) ; } ) ; test ( '最大長以上の値でStockIdを生成するとエラーを投げる' , ( ) => { const longValue = 'a' . repeat ( StockId . MAX_LENGTH + 1 ) ; expect ( ( ) => new StockId ( longValue ) ) . toThrowError ( new Error ( ` StockIdは ${ StockId . MIN_LENGTH } 文字以上、 ${ StockId . MAX_LENGTH } 文字以下でなければなりません。 ` ) ) ; } ) ; } ) ; QuantityAvailable src/Domain/models/Book/Stock/QuantityAvailable/QuantityAvailable.ts import { ValueObject } from 'Domain/models/shared/ValueObject' ; type QuantityAvailableValue = number ; export class QuantityAvailable extends ValueObject < QuantityAvailableValue , 'QuantityAvailable' > { static readonly MAX : number = 1000000 ; static readonly MIN : number = 0 ; constructor ( value : QuantityAvailableValue ) { super ( value ) ; } protected validate ( value : QuantityAvailableValue ) : void { if ( value < QuantityAvailable . MIN || value > QuantityAvailable . MAX ) { throw new Error ( ` 在庫数は ${ QuantityAvailable . MIN } から ${ QuantityAvailable . MAX } の間でなければなりません。 ` ) ; } } increment ( amount : number ) : QuantityAvailable { const newValue = this . _value + amount ; return new QuantityAvailable ( newValue ) ; } decrement ( amount : number ) : QuantityAvailable { const newValue = this . _value - amount ; return new QuantityAvailable ( newValue ) ; } } src/Domain/models/Book/Stock/QuantityAvailable/QuantityAvailable.test.ts import { QuantityAvailable } from './QuantityAvailable' ; describe ( 'QuantityAvailable' , ( ) => { it ( '許容される範囲内の在庫数を設定できる' , ( ) => { const validQuantityAvailable = 500 ; const quantity = new QuantityAvailable ( validQuantityAvailable ) ; expect ( quantity . value ) . toBe ( validQuantityAvailable ) ; } ) ; it ( 'MIN未満の値でQuantityAvailableを生成するとエラーを投げる' , ( ) => { const lessThanMin = QuantityAvailable . MIN - 1 ; expect ( ( ) => new QuantityAvailable ( lessThanMin ) ) . toThrow ( ` 在庫数は ${ QuantityAvailable . MIN } から ${ QuantityAvailable . MAX } の間でなければなりません。 ` ) ; } ) ; it ( 'MAX超の値でQuantityAvailableを生成するとエラーを投げる' , ( ) => { const moreThanMax = QuantityAvailable . MAX + 1 ; expect ( ( ) => new QuantityAvailable ( moreThanMax ) ) . toThrow ( ` 在庫数は ${ QuantityAvailable . MIN } から ${ QuantityAvailable . MAX } の間でなければなりません。 ` ) ; } ) ; describe ( 'increment' , ( ) => { it ( '正の数を加算すると、在庫数が増加する' , ( ) => { const initialQuantity = new QuantityAvailable ( 10 ) ; const incrementAmount = 5 ; const newQuantity = initialQuantity . increment ( incrementAmount ) ; expect ( newQuantity . value ) . toBe ( 15 ) ; } ) ; it ( '最大値を超える加算を試みるとエラーが発生する' , ( ) => { const initialQuantity = new QuantityAvailable ( QuantityAvailable . MAX ) ; const incrementAmount = 1 ; expect ( ( ) => initialQuantity . increment ( incrementAmount ) ) . toThrow ( ` 在庫数は ${ QuantityAvailable . MIN } から ${ QuantityAvailable . MAX } の間でなければなりません。 ` ) ; } ) ; } ) ; describe ( 'decrement' , ( ) => { it ( '正の数を減算すると、在庫数が減少する' , ( ) => { const initialQuantity = new QuantityAvailable ( 10 ) ; const decrementAmount = 5 ; const newQuantity = initialQuantity . decrement ( decrementAmount ) ; expect ( newQuantity . value ) . toBe ( 5 ) ; } ) ; it ( '在庫数を負の数に減算しようとするとエラーが発生する' , ( ) => { const initialQuantity = new QuantityAvailable ( 0 ) ; const decrementAmount = 1 ; expect ( ( ) => initialQuantity . decrement ( decrementAmount ) ) . toThrow ( ` 在庫数は ${ QuantityAvailable . MIN } から ${ QuantityAvailable . MAX } の間でなければなりません。 ` ) ; } ) ; } ) ; } ) ; Status src/Domain/models/Book/Stock/Status/Status.ts import { ValueObject } from 'Domain/models/shared/ValueObject' ; export enum StatusEnum { InStock = 'InStock' , LowStock = 'LowStock' , OutOfStock = 'OutOfStock' , } export type StatusLabel = '在庫あり' | '残りわずか' | '在庫切れ' ; type StatusValue = StatusEnum ; export class Status extends ValueObject < StatusValue , 'Status' > { constructor ( value : StatusValue ) { super ( value ) ; } protected validate ( value : StatusValue ) : void { if ( ! Object . values ( StatusEnum ) . includes ( value ) ) { throw new Error ( '無効なステータスです。' ) ; } } toLabel ( ) : StatusLabel { switch ( this . _value ) { case StatusEnum . InStock : return '在庫あり' ; case StatusEnum . LowStock : return '残りわずか' ; case StatusEnum . OutOfStock : return '在庫切れ' ; } } } src/Domain/models/Book/Stock/Status/Status.test.ts import { Status , StatusEnum } from './Status' ; describe ( 'Status' , ( ) => { it ( '有効なステータスでインスタンスが生成されること' , ( ) => { expect ( new Status ( StatusEnum . InStock ) . value ) . toBe ( StatusEnum . InStock ) ; expect ( new Status ( StatusEnum . OutOfStock ) . value ) . toBe ( StatusEnum . OutOfStock ) ; expect ( new Status ( StatusEnum . LowStock ) . value ) . toBe ( StatusEnum . LowStock ) ; } ) ; it ( '無効なステータスでエラーが投げられること' , ( ) => { const invalidStatus = 'invalid' as StatusEnum ; // テストのために無効な値を渡す expect ( ( ) => new Status ( invalidStatus ) ) . toThrow ( '無効なステータスです。' ) ; } ) ; describe ( 'toLabel()' , ( ) => { it ( 'ステータスInStockが「在庫あり」に変換されること' , ( ) => { const status = new Status ( StatusEnum . InStock ) ; expect ( status . toLabel ( ) ) . toBe ( '在庫あり' ) ; } ) ; it ( 'ステータスOutOfStockが「在庫切れ」に変換されること' , ( ) => { const status = new Status ( StatusEnum . OutOfStock ) ; expect ( status . toLabel ( ) ) . toBe ( '在庫切れ' ) ; } ) ; it ( 'ステータスLowStockが「残りわずか」に変換されること' , ( ) => { const status = new Status ( StatusEnum . LowStock ) ; expect ( status . toLabel ( ) ) . toBe ( '残りわずか' ) ; } ) ; } ) ; } ) ; ! 値オブジェクトをすべて手動で作成するのは大変な作業です。そこで、値オブジェクトを自動生成するツールを作成するか、ChatGPT などの生成 AI を用いて、コードを 自動生成 することをオススメします。 最後に全値オブジェクトのテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest 以上で全ての値オブジェクトの実装が完了しました。 まとめ 値オブジェクトは値である 値オブジェクトを利用することで、不正な値が存在する可能性を減らすことができる 値オブジェクト自身がドメイン内の値のドキュメントになる 本章では、値オブジェクトの実装方法と、値オブジェクトのメリットについて学びました。値オブジェクトは、ドメインの知識を表現するために欠かせない重要な概念です。本章では取り扱いませんでしたが、 StringValueObject 、 NumberValueObject 、 EnumValueObject など、もう一段抽象的なクラスを作成したりプラス α の機能を追加するなどのカスタマイズも可能です。ぜひ、値オブジェクトを活用してみてください。 次章は、今回作成した値オブジェクトを利用して、エンティティを実装していきます。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/valueObject 参考文献 https://qiita.com/suin/items/57cfc0ec9bb1a6995aa5 PREV 第2部 戦術的設計 (コード実装) NEXT エンティティ (Entity) GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter5_event_storming
================================================================================

このチャプターの目次 イベントストーミングとは イベントストーミングマップ イベントストーミングのルール イベントストーミングの進め方 オンライン書店を例に実践 まとめ 参考文献 イベントストーミングとは イベントストーミング (Event Storming) とは、 複雑なビジネスプロセスやドメインの知識を共有、可視化、そして理解するための協同作業ベースのモデリング手法 です。 伝統的なモデリングや要件定義の手法は、多くの場合、ビジネスステークホルダーとソフトウェア開発者の間にコミュニケーションのギャップが生じる可能性がありました。イベントストーミングは、このギャップを埋めるために生み出されました。この手法は、関係者が同じ空間に集まり、ホワイトボードや Miro などのオンラインコラボレーションツールを用いて直感的でインタラクティブな方法でドメインの知識を共有し、問題点やビジネスルールを特定することを目的としています。 イベントストーミングマップ 本章でイベントストーミングを通じて作成された最終的なイベントストーミングマップは以下の通りです。この図では、オンライン書店のドメインにおける主要なドメインイベントと、それらを実行するためのプロセスが示されています。また、コンテキストの境界も定義されています。今回は省略しますが、これらのコンテキスト間の統合のためにコンテキスト間の関連性を追加し、コンテキストマップを作成することもできます。 それではどのような過程で作成されるか見ていきましょう。 イベントストーミングのルール イベントストーミングにはいくつかの要素とルールが存在します。 まずは登場する主要要素を 7 つ紹介します。 ドメインイベント (Domain event) ドメインイベントとは「過去に発生した出来事」を表します。イベントは過去に起きるものなので過去形の動詞で表現されます (例:「注文が承認された」「商品が出荷された」など) 。ドメインイベントはビジネス上の重要な出来事や変更を示すもので、ドメインの中心的な要素として捉えられます。 ポリシー (Policy) ドメインイベントが発生したときに何をすべきかを定義するビジネスルールやロジックを表します。ポリシーはあるドメインイベントに応じて実行すべきコマンドを決定するためのガイドラインとなります。 コマンド (Command) システムに何かを 行うように という指示や命令を表します。その結果としてドメインイベントが発生します。 (例:「注文を承認する」「商品を出荷する」など) アクター (Actor) コマンドを発行する人やシステムのことです。アクターは外部の利用者やシステムであり、特定のコマンドを実行することでドメインイベントを発生させます。 ビュー/リードモデル (View/Read model) システムの状態や情報を参照するための表現です。これは通常、ユーザーインターフェイスで提供されるデータの表現を指します。 集約 (Aggregates) 集約とは、関連するデータとそのデータを操作するビジネスルールや制約を一つのグループにまとめたものです。これにより、データの不整合を防ぐための「一貫性の境界」が設けられ、この境界内でのみデータの状態変更が可能です。集約は複数の値オブジェクトやエンティティをまとめ、それらが共に働くことでビジネスの要件を満たすよう設計された、データとロジックのカプセル化された集合体です。 ! 値オブジェクト (chapter08) 、エンティティ (chapter09)、集約 (chapter10)の詳細や実装はそれぞれの章で説明します。 イベントストーミングの段階で完璧な集約を定義することは難しいです。 プロジェクトが進行するにつれてその定義を洗練させていくのが良いでしょう。 外部システム その名の通り外部のシステムです。たとえばメール送信に SendGrid 、決済に Stripe Payments などを利用する場合、それらは外部システムとなります。 そしてこれらの要素には次の図のような関係性があります。 あるドメインイベントが別のドメインイベントを発生させる場合、以下のように各要素を経由してドメインイベント同士の関係性を表現しなければいけないというルールがあります。 ドメインイベント -> ビュー/リードモデル -> アクター -> コマンド -> 外部システム/集約 -> ドメインイベント ドメインイベント -> ポリシー -> コマンド -> 外部システム/集約 -> ドメインイベント 複数の関係者やシステムがどう絡み合っているのかを理解するため、参加者全員で情報を出し合い、誰がどんな行動を取り、それによってどんな出来事が起こるのかを一緒に書き出していきます。このやり取りを繰り返すことで、全体の流れが見えやすくなり、より明確にドメインを理解できるようになります。 イベントストーミングの進め方 フローを細分化して説明していきます。 参加者の選定 ドメインエキスパート ソフトウェア開発者 プロダクトオーナーやプロジェクトマネージャー UX/UI デザイナーなどの関連するステークホルダー ! 構築したいサービスに UI が必要となる場合、UI/UX デザイナーも同席することをオススメします。理由は 3 点です。 直感的なインターフェイスの議論 システムの動作やプロセスを議論する際、それをサポートする UI の要件や挙動に関するディスカッションも必要となることがあります。デザイナーが同席することで、これらの議論がより具体的かつ効果的に進行します。 プロトタイピングの迅速なフィードバック イベントストーミングの内容をもとにプロトタイプやモックアップを作成し、関係者にフィードバックをもらうことは非常に重要です。デザイナーが同席することでその場で迅速かつ効果的なフィードバックを得ることができます。 UI/UX の向上 システムやサービスの成功は、技術的な実装だけでなく、最終的な UI/UX にも大きく依存します。デザイナーが初期段階から関与することで、最終的な製品の品質を向上させることができます。 ツールの選定 ホワイトボード (オフライン) Miro 、 FigJam などのオンラインコラボレーションツール ドメインイベントの洗い出し 主要なドメインで起きるドメインイベントを参加者全員で洗い出します。会話の中で、「〜するときに」「〜の場合は」「〜が発生したときに」といった言葉が出てきたら、それがドメインイベントの手がかりです。この時に何となくの時系列で配置しておくと 4 の手間が軽減します。 ドメインイベント同士を時系列で繋げる ドメインイベント同士に繋がりがある場合、ドメインイベントと同士を時系列の流れに合わせて矢印で繋ぎます。 ドメインイベント同士のギャップを埋める 前述したルールに則ってドメインイベント間のギャップを埋めていきます。 a. 疑問、懸念事項、不確実要素をメモする イベントストーミングを通して発生した疑問、懸念事項、不確実要素などをホットスポットとしてログを残します。この作業はどのタイミングで行っても大丈夫です。 集約を特定する コマンドの目的語に当たるものが集約である可能性が高いです。たとえば「注文を承認する」がコマンドの場合「注文」が集約になります。 境界づけられたコンテキストの定義、コアドメイン、サブドメインを特定する 6 で集約を特定した後に同一名称の集約が複数存在する場合があります。その場合集約同士を比較し、言葉が持つ意味や関連情報が異なる場合、コンテキストの境界になる可能性があります。適当に線を引き区分けしましょう。その後、それぞれのコンテキストで、それがコアドメインなのかサブドメインなのかを特定します。 ! 上記の順番を完璧に守る必要はありません。フローが進むにつれて新しいドメインイベントの発見や、考慮漏れが必ず発生するのでその都度書き加えるなど柔軟に対応しましょう。 オンライン書店を例に実践 これまでドメインの一例として扱ってきたオンライン書店で実際にイベントストーミングを行なってみましょう。 ! ここでの内容はオンライン書店のビジネスモデルや要件に応じて異なります。シンプルにするため、簡略化した内容となっています。あくまでイベントストーミングの手法を理解するための例としてご覧ください。 また、著者はオンライン書店のドメインエキスパートではございません。 書店や EC 領域の解釈に間違いがある可能性がございます。あらかじめご了承ください。 参加者の選定 今回は著者一名で行います。 ツールの選定 今回は FigJam を利用していきます。 ドメインイベントの洗い出し ドメインイベント同士を時系列で繋げる ドメインイベント同士のギャップを埋める ここではプロセス中に考慮漏れに気づき、ドメインイベントを追加しています。 a. 疑問、懸念事項、不確実要素をメモする 集約を特定する 境界づけられたコンテキストの定義、コアドメイン、サブドメインを特定する 以上でイベントストーミングは完了です。 複数のコンテキストが存在することがわかりました。今回は省略していますが、実際にはこれら以外にも「決済コンテキスト」や「認証コンテキスト」などのコンテキストが存在するでしょう。 イベントストーミングを通じて作成された図は、ドメインにとって重要なワークフローを視覚的に表現するものです。このプロセスは、関係者がビジネスの要件、ルールを理解し、システムの振る舞いや相互作用を明確に把握するのに非常に役立ちます。しかしながら、ビジネスや市場のニーズは時間とともに変化します。したがって、イベントストーミングが一度完了したとしても、その成果物は静的なものではなく、進化し続けるドメインの動きを反映するために定期的なレビューと更新が必要となります。 まとめ イベントストーミングは、複雑なビジネスプロセスやドメインの知識を共有、可視化、そして理解するための協同作業ベースのモデリング手法 変化に対応するため定期的更新が必要 本章では「コアドメイン、サブドメインの特定」「境界づけられたコンテキストの定義」「エンティティの特定 (ビジネス要件の確認、エンティティの識別) 」「ビジネスルールの把握」を行いました。 次章では戦術的設計に向けて「エンティティの特定(属性の識別、エンティティ間の関連の定義)」を、よりわかりやすい形で可視化していきます。 ツールには PlantUML を利用し、コードとして管理する流れを紹介します。 参考文献 https://www.eventstorming.com/ https://www.youtube.com/watch?v=jC9lE4YqgyY PREV ドメインモデリング NEXT ドメインモデル図の作成 GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter9_entity
================================================================================

このチャプターの目次 エンティティとは 値オブジェクトとの違い エンティティの特徴 エンティティの実装 実装 ビジネスルールの適用 エンティティのテスト まとめ これまでのコード エンティティとは エンティティ (Entity) とは、値オブジェクトと並びドメインモデル (ドメインオブジェクト) の中心的な要素で、ドメイン内のさまざまな ビジネスの実体 の概念をモデル化するのに用いられます。たとえばエンティティには書籍、在庫、ユーザー、履歴などが挙げられます。 値オブジェクトとの違い エンティティと値オブジェクトは、どちらもドメインモデルの中心的な要素ですが、別物です。それらを区別する概念は 同一性 (Identity) にあります。 エンティティの同一性 エンティティは「誰であるか」や「何であるか」という概念によって同一性が定義されます。エンティティを説明するのによく「人」が例に挙げられます。人には名前や住所、年齢などの属性がありますが、それらの属性が変わっても、その人は同じ人であり続けます。たとえば、誕生日を迎え、年齢が変わってもその人は同じ人です。エンティティの同一性は、それを構成する属性の値に依存しません。たとえ属性が変わっても、そのエンティティの同一性は変わりません。 そして、エンティティは同一であるという概念を、一意に識別する 「ID」 を割り当てることで表現します。この 「ID」 によってエンティティのインスタンスは区別されます。たとえ属性が時間の経過と共に変わっても、その「ID」 が同じであれば、それは同一のエンティティと見なされます。 class Person { constructor ( public readonly personId : string , public name : string , public age : number , public address : string ) { } } // 山田太郎さんが誕生 const person1 = new Person ( '1' , '山田太郎' , 0 , '東京都' ) ; person1 . age = 1 ; // 東京の山田太郎さんが誕生日を迎え、年齢が1歳になった。 // 一意な識別子「personId」が同一であるため、同一のエンティティと見なされる console . log ( person1 ) ; // Person { personId: '1', name: '山田太郎', age: 1, address: '東京都' } 値オブジェクトの同一性 一方で、値オブジェクトはその属性によって同一性が定義されるものです。値オブジェクトには識別子がなく、その属性の値がすべてであり、それらの値が同じであれば、それは同じ値オブジェクトと見なされます。値オブジェクトの同一性は、それを構成する属性の値の組み合わせに依存します。たとえば、 BookId を表す値オブジェクトが「9784167158057」という属性を持ち、もしほかの BookId 値オブジェクトもまったく同じ属性を持っていれば、それらは区別されず同じものとして扱われます。詳しくは chapter08 値オブジェクト で説明しています。 const bookId1 = new BookId ( '9784167158057' ) ; const bookId2 = new BookId ( '9784167158057' ) ; // 同一であることの確認 console . log ( bookId1 . equals ( bookId2 ) ) ; // true エンティティの特徴 値同様エンティティにもいくつかの特徴があります。 一意な識別子によって区別される 可変である ライフサイクルがある ではそれぞれ確認していきましょう。 一意な識別子によって区別される さきほど説明した通り、エンティティはその一意な識別子によって区別されます。この識別子はエンティティが生成された瞬間に割り当てられ、そのライフサイクルの終わりまで変わることはありません。この識別子のおかげで、属性が時間と共に変化しても、エンティティの同一性は保たれ続けます。 可変である エンティティは値オブジェクトとは反対に、その状態が変更可能です。属性や関連するオブジェクトが変更されることがあり、エンティティの状態はその内容を反映できます。 ライフサイクルがある エンティティには明確なライフサイクルが存在します。生成、変更、そして場合によっては削除というプロセスを経ることで、エンティティは時間の経過と共にビジネスプロセスに沿って変化します。 エンティティの実装 エンティティの特徴が確認できたので、実際に Stock エンティティを例にエンティティを実装していきましょう。まずはドメインモデリングで作成した Stock エンティティを振り返ってみましょう。 Stock エンティティが持つ属性やビジネスルールは以下の通りです。 src/Domain/models/Book/Stock/Stock.pu @startuml Stock !include ./Status/Status.pu !include ./QuantityAvailable/QuantityAvailable.pu !include ./StockId/StockId.pu class "Stock(在庫)" as Stock << ( E , green ) Entity >> { StockId : StockId
    QuantityAvailable : 在庫数
    Status : ステータス } Stock *-down- StockId
Stock *-down- QuantityAvailable
Stock *-down- Status note bottom of Stock
    - 初回作成時、ステータスは「在庫切れ」から始まる。
    - 在庫数は0の場合は在庫切れ。10以下の場合は残りわずか。それ以外は在庫あり。 end note @enduml 実装 それでは Stock.pu と同じディレクトリに Stock.ts ファイルを作成し以下のように実装します。 src/Domain/models/Book/Stock/Stock.ts import { QuantityAvailable } from './QuantityAvailable/QuantityAvailable' ; import { Status , StatusEnum } from './Status/Status' ; import { StockId } from './StockId/StockId' ; export class Stock { private constructor ( private readonly _stockId : StockId , // 識別子は変更不可のためreadonlyにする private _quantityAvailable : QuantityAvailable , private _status : Status ) { } // 新規エンティティの生成 static create ( stockId : StockId , quantityAvailable : QuantityAvailable , status : Status ) { return new Stock ( stockId , quantityAvailable , status ) ; } public delete ( ) { // 削除時のロジックがあれば書く } public changeStatus ( newStatus : Status ) { this . _status = newStatus ; } public changeQuantityAvailable ( newQuantityAvailable : QuantityAvailable ) { this . _quantityAvailable = newQuantityAvailable ; } // エンティティの再構築 static reconstruct ( stockId : StockId , quantityAvailable : QuantityAvailable , status : Status ) { return new Stock ( stockId , quantityAvailable , status ) ; } get stockId ( ) : StockId { return this . _stockId ; } get quantityAvailable ( ) : QuantityAvailable { return this . _quantityAvailable ; } get status ( ) : Status { return this . _status ; } } ! private constructor にしている理由は、 create メソッドと reconstruct メソッドのみでエンティティを生成することを強制 するためです。この後 create メソッドでは、エンティティの生成時の制御 (ビジネスルールの適用) を行います。 reconstruct メソッドは、データベースなどから読み込んだデータをもとにエンティティを再構築する際に使用します。 reconstruct メソッドは chapter12 リポジトリ で利用します。 それでは、Stock クラスを用いて、エンティティの特徴がどのように実装されているかを確認しましょう。 一意な識別子によって区別される StockId は一意な識別子です。この StockId はエンティティの生成時に割り当てられ、そのライフサイクルの終わりまで変わることはありません。そのため、 StockId はエンティティの生成時でのみ設定され、その後変更されることはありません。 readonly修飾子 を用いて、生成時以外で変更されることを防ぎます。 const stock : Stock = Stock . create ( new StockId ( ) , new QuantityAvailable ( 0 ) , new Status ( StatusEnum . OutOfStock ) ) ; stock . stockId = new StockId ( 'stockId2' ) ; // StockIdはreadonlyなので変更できない 可変である 自身に定義されているメソッドを用いて、エンティティの状態を変更することができます。 const stock : Stock = Stock . create ( new StockId ( ) , new QuantityAvailable ( 0 ) , new Status ( StatusEnum . OutOfStock ) ) ; stock . changeStatus ( new Status ( StatusEnum . OnSale ) ) ; console . log ( stock . status ) ; // Status { _value: 'OnSale' } stock . changeQuantityAvailable ( new QuantityAvailable ( 100 ) ) ; console . log ( stock . quantityAvailable ) ; // QuantityAvailable { _value: 100 } ライフサイクルがある create 、 change 、 delete メソッドを用いて、エンティティのライフサイクルを表現することができます。 const stock : Stock = Stock . create ( new StockId ( ) , new QuantityAvailable ( 0 ) , new Status ( StatusEnum . OutOfStock ) ) ; stock . changeStatus ( new Status ( StatusEnum . OnSale ) ) ; stock . delete ( ) ; ビジネスルールの適用 実装したエンティティはまだ未完成です。今のままではビジネスルールに反したエンティティのライフサイクルが発生してしまいます。たとえば 在庫数が0 の状態でステータスが 在庫あり のエンティティが生成できてしまいます。そこで、エンティティのライフサイクルにビジネスルールを適用する必要があります。それでは、ビジネスルールを適用していきましょう。 src/Domain/models/Book/Stock/Stock.ts import { QuantityAvailable } from './QuantityAvailable/QuantityAvailable' ; import { Status , StatusEnum } from './Status/Status' ; import { StockId } from './StockId/StockId' ; export class Stock { private constructor ( private readonly _stockId : StockId , private _quantityAvailable : QuantityAvailable , private _status : Status ) { } // 新規エンティティの生成 static create ( ) { const defaultStockId = new StockId ( ) ; // 自動ID採番 const defaultQuantityAvailable = new QuantityAvailable ( 0 ) ; const defaultStatus = new Status ( StatusEnum . OutOfStock ) ; return new Stock ( defaultStockId , defaultQuantityAvailable , defaultStatus ) ; } delete ( ) { if ( this . status . value !== StatusEnum . OutOfStock ) { throw new Error ( '在庫がある場合削除できません。' ) ; } } private changeStatus ( newStatus : Status ) { this . _status = newStatus ; } // 在庫数を増やす increaseQuantity ( amount : number ) { if ( amount < 0 ) { throw new Error ( '増加量は0以上でなければなりません。' ) ; } const newQuantity = this . quantityAvailable . increment ( amount ) . value ; // 在庫数が10以下ならステータスを残りわずかにする if ( newQuantity <= 10 ) { this . changeStatus ( new Status ( StatusEnum . LowStock ) ) ; } this . _quantityAvailable = new QuantityAvailable ( newQuantity ) ; } // 在庫数を減らす decreaseQuantity ( amount : number ) { if ( amount < 0 ) { throw new Error ( '減少量は0以上でなければなりません。' ) ; } const newQuantity = this . quantityAvailable . decrement ( amount ) . value ; if ( newQuantity < 0 ) { throw new Error ( '減少後の在庫数が0未満になってしまいます。' ) ; } // 在庫数が10以下ならステータスを残りわずかにする if ( newQuantity <= 10 ) { this . changeStatus ( new Status ( StatusEnum . LowStock ) ) ; } // 在庫数が0になったらステータスを在庫切れにする if ( newQuantity === 0 ) { this . changeStatus ( new Status ( StatusEnum . OutOfStock ) ) ; } this . _quantityAvailable = new QuantityAvailable ( newQuantity ) ; } // エンティティの再構築 static reconstruct ( stockId : StockId , quantityAvailable : QuantityAvailable , status : Status ) { return new Stock ( stockId , quantityAvailable , status ) ; } get stockId ( ) : StockId { return this . _stockId ; } get quantityAvailable ( ) : QuantityAvailable { return this . _quantityAvailable ; } get status ( ) : Status { return this . _status ; } } create メソッドでは、デフォルトの値を設定しています。このデフォルトの値は、たとえば「初期在庫数は 0 、初期ステータスは 在庫切れ 」というように、ビジネスルールによって決まった値です。このようにすることで生成時は必ずビジネスルールに従ったエンティティが生成されるようになります。 static create ( ) { const defaultStockId = new StockId ( ) ; // 自動ID採番 const defaultQuantityAvailable = new QuantityAvailable ( 0 ) const defaultStatus = new Status ( StatusEnum . OutOfStock ) ; return new Stock ( defaultStockId , defaultQuantityAvailable , defaultStatus ) ; } delete メソッドでは、「ステータスが在庫切れである場合のみ在庫を削除できる」というビジネスルールを適用しています。このように、エンティティの状態を変更するメソッドの中で、ビジネスルールを適用することで、エンティティのライフサイクルにビジネスルールを適用することができます。 delete ( ) { if ( this . status . value !== StatusEnum . OutOfStock ) { throw new Error ( '在庫がある場合削除できません。' ) ; } } changeQuantityAvailable メソッドは、 increaseQuantity 、 decreaseQuantity メソッドに変更されました。エンティティのメソッドはドメインの振る舞いを反映したものであるべきです。この変更によって、より直感的に在庫数を増減させる操作ができるようになりました。さらに「在庫数が 0 になったらステータスを在庫切れに変更する」というビジネスルールや、在庫数の整合性のルールを適用しています。 // 在庫数を増やす increaseQuantity ( amount : number ) { if ( amount < 0 ) { throw new Error ( '増加量は0以上でなければなりません。' ) ; } const newQuantity = this . quantityAvailable . increment ( amount ) . value ; // 在庫数が10以下ならステータスを残りわずかにする if ( newQuantity <= 10 ) { this . changeStatus ( new Status ( StatusEnum . LowStock ) ) ; } this . _quantityAvailable = new QuantityAvailable ( newQuantity ) ; } // 在庫数を減らす decreaseQuantity ( amount : number ) { if ( amount < 0 ) { throw new Error ( '減少量は0以上でなければなりません。' ) ; } const newQuantity = this . quantityAvailable . decrement ( amount ) . value ; if ( newQuantity < 0 ) { throw new Error ( '減少後の在庫数が0未満になってしまいます。' ) ; } // 在庫数が10以下ならステータスを残りわずかにする if ( newQuantity <= 10 ) { this . changeStatus ( new Status ( StatusEnum . LowStock ) ) ; } // 在庫数が0になったらステータスを在庫切れにする if ( newQuantity === 0 ) { this . changeStatus ( new Status ( StatusEnum . OutOfStock ) ) ; } this . _quantityAvailable = new QuantityAvailable ( newQuantity ) ; } ! エンティティが持つ属性は private にして、メソッドを通して変更するようにしましょう。属性を変更するメソッドにビジネスルールを適用することで、ビジネスルールの整合性が崩れるのを防ぐことができます。 これらの実装により、エンティティにビジネスルールを適用することができました。 エンティティのテスト 値オブジェクト同様、ビジネスルールが正しく実装されているかを保証するためにはテストは必須です。 Stock.ts と同じディレクトリに Stock.test.ts を作成し、以下のように実装します。 src/Domain/models/Book/Stock/Stock.test.ts import { Stock } from './Stock' ; import { QuantityAvailable } from './QuantityAvailable/QuantityAvailable' ; import { Status , StatusEnum } from './Status/Status' ; import { StockId } from './StockId/StockId' ; // nanoid() をモックする jest . mock ( 'nanoid' , ( ) => ( { nanoid : ( ) => 'testIdWithExactLength' , } ) ) ; describe ( 'Stock' , ( ) => { const stockId = new StockId ( 'abc' ) ; const quantityAvailable = new QuantityAvailable ( 100 ) ; const status = new Status ( StatusEnum . InStock ) ; describe ( 'create' , ( ) => { it ( 'デフォルト値で在庫を作成する' , ( ) => { const stock = Stock . create ( ) ; expect ( stock . stockId . equals ( new StockId ( 'testIdWithExactLength' ) ) ) . toBeTruthy ( ) ; expect ( stock . quantityAvailable . equals ( new QuantityAvailable ( 0 ) ) ) . toBeTruthy ( ) ; expect ( stock . status . equals ( new Status ( StatusEnum . OutOfStock ) ) ) . toBeTruthy ( ) ; } ) ; } ) ; describe ( 'delete' , ( ) => { it ( '在庫ありの場合はエラーを投げる' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; expect ( ( ) => stock . delete ( ) ) . toThrow ( '在庫がある場合削除できません。' ) ; } ) ; it ( '在庫なしなしの場合はエラーを投げない' , ( ) => { const notOnSaleStatus = new Status ( StatusEnum . OutOfStock ) ; const stock = Stock . reconstruct ( stockId , quantityAvailable , notOnSaleStatus ) ; expect ( ( ) => stock . delete ( ) ) . not . toThrow ( ) ; } ) ; } ) ; describe ( 'increaseQuantity' , ( ) => { it ( '在庫数を増やす' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; stock . increaseQuantity ( 5 ) ; expect ( stock . quantityAvailable . equals ( new QuantityAvailable ( 105 ) ) ) . toBeTruthy ( ) ; } ) ; it ( '増加量が負の数の場合はエラーを投げる' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; expect ( ( ) => stock . increaseQuantity ( - 1 ) ) . toThrow ( '増加量は0以上でなければなりません。' ) ; } ) ; } ) ; describe ( 'decreaseQuantity' , ( ) => { it ( '在庫数を減らす' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; stock . decreaseQuantity ( 5 ) ; expect ( stock . quantityAvailable . equals ( new QuantityAvailable ( 95 ) ) ) . toBeTruthy ( ) ; } ) ; it ( '減少量が負の数の場合はエラーを投げる' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; expect ( ( ) => stock . decreaseQuantity ( - 1 ) ) . toThrow ( '減少量は0以上でなければなりません。' ) ; } ) ; it ( '減少後の在庫数が0未満になる場合はエラーを投げる' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; expect ( ( ) => stock . decreaseQuantity ( 101 ) ) . toThrow ( ) ; } ) ; it ( '在庫数が0になったらステータスを在庫切れにする' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; stock . decreaseQuantity ( 100 ) ; expect ( stock . quantityAvailable . equals ( new QuantityAvailable ( 0 ) ) ) . toBeTruthy ( ) ; expect ( stock . status . equals ( new Status ( StatusEnum . OutOfStock ) ) ) . toBeTruthy ( ) ; } ) ; it ( '在庫数が10以下になったらステータスを残りわずかにする' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; stock . decreaseQuantity ( 90 ) ; expect ( stock . quantityAvailable . equals ( new QuantityAvailable ( 10 ) ) ) . toBeTruthy ( ) ; expect ( stock . status . equals ( new Status ( StatusEnum . LowStock ) ) ) . toBeTruthy ( ) ; } ) ; } ) ; } ) ; ビジネスロジック、振る舞い (メソッド) 、例外処理などを網羅するようにテストを書きます。なるべくテストのカバレッジが 100%に近づけるようにしましょう。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest Stock.test.ts 以上で Stock エンティティの実装は完了です。ビジネスルールをクラス内にカプセル化し、 Stock エンティティ自身がドキュメントの役割を果たすようになりました。さらに、ビジネスルールを保ったままライフサイクルを変化させることができるようになりました。 まとめ エンティティを利用することで、ライフサイクルの整合性を担保できる エンティティ自身がドキュメントになる 本章では、値オブジェクトとエンティティの違い、エンティティの実装方法について学びました。 次章は、ドメイン駆動設計の中でも非常に重要で難しい概念である 集約 の説明を行い、 集約ルート である Book ルートエンティティを実装していきます。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/entity PREV 値オブジェクト (Value Object) NEXT 集約 (Aggregate) GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter10_aggregate
================================================================================

このチャプターの目次 集約とは Book 集約の実装 デメテルの法則 実装 Book 集約のテスト 集約の設計上のルール 集約は小さくする 集約間は識別子(ID)で参照する 集約の外部では結果整合性を用いる まとめ これまでのコード 参考文献 集約とは 集約 (Aggregate) とは、関連するオブジェクト群を 1 つのユニットとして管理するための手法です。集約は、1 つの ルートエンティティ （集約ルート）と、それに関連するエンティティや値オブジェクトで構成されます。集約は、ビジネスルールとデータの 整合性 を維持するために設計されます。たとえば、「書籍」と「在庫」には整合性が必要です。書籍を削除できるのは在庫が存在しない場合のみで、書籍を削除したら在庫データも削除されなければデータに矛盾が生じてしまいます。このような整合性を保つためには、書籍と在庫を集約として定義する必要があります。 また、集約は リポジトリ の入出力の単位で、集約単位で入出力することで整合性が保たれたデータを確実に管理することができます。集約の設計が適切であれば、データベースに保存されているデータは、ビジネスルールに従って常に一貫性を保つことが可能になります。 ! リポジトリとはデータの永続化を行うコンポーネントで、入出力の単位は 集約 となります。集約で整合性が保たれたデータをそのままデータベースへ反映するために、集約内のデータは同一のトランザクション内ですべて更新されます。 リポジトリの詳細は chapter11 リポジトリ の章で学びます。 Book 集約の実装 それでは、集約ルートである Book ルートエンティティを実装していきましょう。まずはドメインモデリングで作成した BookAggregation を振り返ってみましょう。複数の値オブジェクトとエンティティ (Stock) を属性として持っていることがわかります。 デメテルの法則 コードの実装に入る前に集約にとって重要な デメテルの法則 (Law of Demeter) について説明します。デメテルの法則は、オブジェクト指向プログラミングにおける設計原則の一つです。この法則は、とくにオブジェクト間の相互作用に焦点を当てており、「最小知識の原則」とも呼ばれます。この原則に従うことで、システム内の異なるオブジェクト間の密な結合を減らし、より保守しやすく、理解しやすいコードを作成できます。 デメテルの法則の基本原則 wikipedia ではデメテルの法則では、 オブジェクト O 上の メソッド M が呼び出してもよいメソッドは以下のオブジェクトに属するメソッドのみに限定されると説明されています。 O それ自身 M の引数に渡されたオブジェクト M の内部でインスタンス化されたオブジェクト O を直接的に構成するオブジェクト（O のインスタンス変数） 文章だけでは理解しづらいので、簡単な例を用いて確認しましょう。 // デメテルの法則適用前のコード例 class Stock { constructor ( public _quantityAvailable : number ) { } get quantityAvailable ( ) : number { return this . _quantityAvailable ; } } class Book { constructor ( public stock : Stock ) { } } const quantityAvailable = new Book ( new Stock ( 100 ) ) . stock . quantityAvailable ; この例では、 Book クラスは Stock クラスを通じて quantityAvailable にアクセスしています。これは Book クラスが Stock クラス の内部構造（Stock が quantityAvailable を持っていること）に依存しており、結合度が高いです。 // デメテルの法則適用後のコード例 class Stock { constructor ( public _quantityAvailable : QuantityAvailable ) { } get quantityAvailable ( ) : QuantityAvailable { return this . _quantityAvailable ; } } class Book { constructor ( private stock : Stock ) { } getQuantityAvailable ( ) { return this . stock . quantityAvailable ; } } const quantityAvailable = new Book ( new Stock ( 100 ) ) . getQuantityAvailable ( ) ; この例では、 Book クラス は 自身のメソッドを通じて Stock クラスの quantityAvailable を取得しています。これにより、 Book クラスは Stock クラス の内部構造に依存することなく、より疎結合なコードになります。 集約とデメテルの法則 デメテルの法則は集約実装のガイドラインとなります。集約は、関連オブジェクトを 1 つのユニットとして管理します。集約の内部オブジェクトが密接に関連し合っている一方で、集約の外部からの操作は必ず集約ルートを介して行われなければいけません。上記の例で言うと、 Stock エンティティは Book 集約ルートでのみ操作できるように制御する必要があります。これはデメテルの法則に沿っています。このようにすることで、集約外のオブジェクトが集約内の詳細について知る必要がなくなるため、疎結合になります。 また、集約の内部構造や状態に直接アクセスすることができなくなるため、ドメインルールの漏洩や整合性が破壊されることを防ぐことができます。 実装 それでは Book.pu と同じディレクトリ内に Book.ts ファイルを作成し、デメテルの法則にしたがって Book ルートエンティティを以下のように実装します。 src/Domain/models/Book/Book.ts import { BookId } from './BookId/BookId' ; import { Price } from './Price/Price' ; import { StatusEnum } from './Stock/Status/Status' ; import { Stock } from './Stock/Stock' ; import { Title } from './Title/Title' ; export class Book { private constructor ( private readonly _bookId : BookId , private _title : Title , private _price : Price , private readonly _stock : Stock ) { } static create ( bookId : BookId , title : Title , price : Price ) { return new Book ( bookId , title , price , Stock . create ( ) ) ; } static reconstruct ( bookId : BookId , title : Title , price : Price , stock : Stock ) { return new Book ( bookId , title , price , stock ) ; } delete ( ) { // stockが削除可能か確認する this . _stock . delete ( ) ; // Bookを削除する処理があればここに書く } changeTitle ( newTitle : Title ) { this . _title = newTitle ; } changePrice ( newPrice : Price ) { this . _price = newPrice ; } // 販売可能かどうか isSaleable ( ) { return ( this . _stock . quantityAvailable . value > 0 && this . _stock . status . value !== StatusEnum . OutOfStock ) ; } increaseStock ( amount : number ) { this . _stock . increaseQuantity ( amount ) ; } decreaseStock ( amount : number ) { this . _stock . decreaseQuantity ( amount ) ; } get bookId ( ) : BookId { return this . _bookId ; } get title ( ) : Title { return this . _title ; } get price ( ) : Price { return this . _price ; } get stockId ( ) { return this . _stock . stockId ; } get quantityAvailable ( ) { return this . _stock . quantityAvailable ; } get status ( ) { return this . _stock . status ; } } 基本的な設計方針は前章で学んだエンティティと同じです。違いは Stock エンティティの値やメソッドへの参照がすべて Book ルートエンティティを介して行われている点です。これにより、デメテルの法則に沿ったコードになっています。 Book 集約のテスト それではテストを書いていきましょう。 Book.ts と同じディレクトリに Book.test.ts を作成し、以下のように実装します。 src/Domain/models/Book/Book.test.ts import { Book } from './Book' ; import { BookId } from './BookId/BookId' ; import { Title } from './Title/Title' ; import { Price } from './Price/Price' ; import { Stock } from './Stock/Stock' ; import { StockId } from './Stock/StockId/StockId' ; import { QuantityAvailable } from './Stock/QuantityAvailable/QuantityAvailable' ; import { Status , StatusEnum } from './Stock/Status/Status' ; // nanoid() をモックする jest . mock ( 'nanoid' , ( ) => ( { nanoid : ( ) => 'testIdWithExactLength' , } ) ) ; describe ( 'Book' , ( ) => { const stockId = new StockId ( 'abc' ) ; const quantityAvailable = new QuantityAvailable ( 100 ) ; const status = new Status ( StatusEnum . InStock ) ; const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; const bookId = new BookId ( '9784167158057' ) ; const title = new Title ( '吾輩は猫である' ) ; const price = new Price ( { amount : 770 , currency : 'JPY' , } ) ; describe ( 'create' , ( ) => { it ( 'デフォルト値で在庫を作成する' , ( ) => { const book = Book . create ( bookId , title , price ) ; expect ( book . bookId . equals ( bookId ) ) . toBeTruthy ( ) ; expect ( book . title . equals ( title ) ) . toBeTruthy ( ) ; expect ( book . price . equals ( price ) ) . toBeTruthy ( ) ; expect ( book . stockId . equals ( new StockId ( 'testIdWithExactLength' ) ) ) . toBeTruthy ( ) ; expect ( book . quantityAvailable . equals ( new QuantityAvailable ( 0 ) ) ) . toBeTruthy ( ) ; expect ( book . status . equals ( new Status ( StatusEnum . OutOfStock ) ) ) . toBeTruthy ( ) ; } ) ; } ) ; describe ( 'delete' , ( ) => { it ( '在庫ありの場合はエラーを投げる' , ( ) => { const book = Book . reconstruct ( bookId , title , price , stock ) ; expect ( ( ) => book . delete ( ) ) . toThrow ( '在庫がある場合削除できません。' ) ; } ) ; it ( '在庫なしの場合はエラーを投げない' , ( ) => { const notOnSaleStatus = new Status ( StatusEnum . OutOfStock ) ; const notQuantityAvailable = new QuantityAvailable ( 0 ) ; const stock = Stock . reconstruct ( stockId , notQuantityAvailable , notOnSaleStatus ) ; const book = Book . reconstruct ( bookId , title , price , stock ) ; expect ( ( ) => book . delete ( ) ) . not . toThrow ( ) ; } ) ; } ) ; describe ( 'isSaleable' , ( ) => { it ( '在庫あり、在庫数が整数の場合はtrueを返す' , ( ) => { const stock = Stock . reconstruct ( stockId , quantityAvailable , status ) ; const book = Book . reconstruct ( bookId , title , price , stock ) ; expect ( book . isSaleable ( ) ) . toBeTruthy ( ) ; } ) ; it ( '在庫なし、在庫数0の場合はfalseを返す' , ( ) => { const notOnSaleStatus = new Status ( StatusEnum . OutOfStock ) ; const notQuantityAvailable = new QuantityAvailable ( 0 ) ; const stock = Stock . reconstruct ( stockId , notQuantityAvailable , notOnSaleStatus ) ; const book = Book . reconstruct ( bookId , title , price , stock ) ; expect ( book . isSaleable ( ) ) . toBeFalsy ( ) ; } ) ; } ) ; describe ( 'increaseStock' , ( ) => { it ( 'stock.increaseQuantityが呼ばれる' , ( ) => { const book = Book . reconstruct ( bookId , title , price , stock ) ; const spy = jest . spyOn ( stock , 'increaseQuantity' ) ; book . increaseStock ( 10 ) ; expect ( spy ) . toHaveBeenCalled ( ) ; } ) ; } ) ; describe ( 'decreaseStock' , ( ) => { it ( 'stock.decreaseQuantityが呼ばれる' , ( ) => { const book = Book . reconstruct ( bookId , title , price , stock ) ; const spy = jest . spyOn ( stock , 'decreaseQuantity' ) ; book . decreaseStock ( 10 ) ; expect ( spy ) . toHaveBeenCalled ( ) ; } ) ; } ) ; describe ( 'changeTitle' , ( ) => { it ( 'titleを変更する' , ( ) => { const book = Book . reconstruct ( bookId , title , price , stock ) ; const newTitle = new Title ( '坊ちゃん' ) ; book . changeTitle ( newTitle ) ; expect ( book . title . equals ( newTitle ) ) . toBeTruthy ( ) ; } ) ; } ) ; describe ( 'changePrice' , ( ) => { it ( 'priceを変更する' , ( ) => { const book = Book . reconstruct ( bookId , title , price , stock ) ; const newPrice = new Price ( { amount : 880 , currency : 'JPY' , } ) ; book . changePrice ( newPrice ) ; expect ( book . price . equals ( newPrice ) ) . toBeTruthy ( ) ; } ) ; } ) ; } ) ; ! increaseStock メソッドではデメテルの法則に従い、 Stock エンティティの increaseQuantity メソッドを呼び出しています。 increaseQuantity メソッドのロジックのテストは Stock.test.ts で行っているため、 Book ルートエンティティの increaseStock メソッドのテストでは、 Stock エンティティの increaseQuantity メソッドが呼び出されていることだけ確認します。そのようにテストすることで、 Stock エンティティの increaseQuantity メソッドのロジックが変更されても、修正は Stock.test.ts だけで済み、 Book.test.ts の修正は不要になります。 ここでは、メソッド呼び出しの確認に jest.spyOn を使用します。 jest.spyOn は、オブジェクトのメソッドが呼び出されたかどうかを確認するためのモック関数を作成します。 jest.spyOn を使用することで、 Stock エンティティの increaseQuantity メソッドが呼び出されていることを確認することができます。 it ( 'stock.increaseQuantityが呼ばれる' , ( ) => { const book = Book . reconstruct ( bookId , title , price , stock ) ; const spy = jest . spyOn ( stock , 'increaseQuantity' ) ; book . increaseStock ( 10 ) ; expect ( spy ) . toHaveBeenCalled ( ) ; } ) ; なるべくテストのカバレッジが 100%に近づけるようにしましょう。 jest コマンドでテストを実行し、テストが成功することを確認します。 StockManagement/ $ jest Book.test.ts エラーなく成功すれば OK です。 集約の設計上のルール 集約の設計には いくつかのルールがあります。ここでは重要な 3 つを紹介します。 集約は小さくする 集約間は識別子 (ID) で参照する 集約の外部では結果整合性を用いる それでは、それぞれ 書籍販売コンテキスト の 書籍集約 を例に用いて説明します。この書籍集約は良い集約とは言えません。なぜ良くない集約なのかを集約の設計上のルールと照らし合わせながら確認してきましょう。 class Book { constructor ( private readonly _bookId : BookId , // 値オブジェクト private _title : Title , // 値オブジェクト private _author : Author , // エンティティ private _publisher : Publisher , // エンティティ private _reviews : Review [ ] , // エンティティ ) { } ( 省略 ) } 集約は小さくする 集約の設計をする上で一番難しいのが、集約の範囲を決めることです。基本的に集約の範囲は、なるべく小さくしたほうが良いとされています。章のはじめに、集約はビジネスルールとデータの 整合性 を維持するために設計すると説明しましたが、「整合性」だけに焦点を当てて設計をしてしまうと集約の範囲が肥大しがちです。書籍集約は肥大した集約の例です。書籍集約は、書籍のタイトルや著者、出版社、レビューなど、書籍に関連するすべての情報を含んでいます。 これらすべてを一つの集約として捉えようとすると、その集約は複雑になり、管理が困難になります。集約は 1 集約 1 トランザクション でデータベースに反映しなければなりません。このため、書籍の一部データを更新するために集約内のすべての要素を一度データベースから読み込み、すべて同時に更新する必要が生じます。これは明らかな オーバーヘッド です。すべての関連要素を一括で扱うことで、システムの パフォーマンスに悪影響 を及ぼします。 また、集約の範囲が大きいと トランザクションのロック 時間が長くなり、データベースのパフォーマンスが悪化したり障害が発生しやすくなります。たとえば「書籍」と「レビュー」は 1対n の関係性にあります。仮にレビュー数が 10000 件ある書籍データを更新する場合、10000 件のレビューを更新する処理が同じトランザクション内で行われます。レビューの更新処理が一つでも失敗した場合、書籍の更新処理も失敗するため、書籍の更新ができなくなってしまいます。 これらの理由から集約はなるべく小さく区切ることが望ましいです。 集約をどこで区切るか 大きな集約がもたらす問題を避けるためには、集約の範囲をどこで区切るかを考える必要があります。集約の範囲に迷った際には、2 つの基準を参考にしてみてください。 ルートエンティティに対して 1対n の関係性にあるエンティティを保有する場合、保有数の上限が適切か 強整合性 (トランザクション整合性) が必要か それでは、それぞれ説明します。 ! ベストな集約の範囲を見つけるのは難しく、正解は複数あります。迷ったときはとりあえず実装してみるのも一つの手です。よくない集約は実装してみることで、扱いにくい、パフォーマンスが悪いなどの問題に気づきます。その結果を踏まえて集約の範囲を見直し、ブラッシュアップすることが大切です。 ルートエンティティに対して 1対n の関係性にあるエンティティを保有する場合、保有数の上限が適切か ルートエンティティに対する関係性は重要な要素です。ルートエンティティと保有するエンティティの間の関係が 1対n の場合、その n の数には上限があるかどうかによって同一集約に含めた方が良いかどうかが決まります。 たとえば、「書籍」と「レビュー」の関係性は 1対n ですが、レビューの数に上限はあるでしょうか？それはビジネスの要件や非機能要件によって変動します。仮にレビュー数の上限が 100 件までと決まっているとします。MAX でも 100 件程度であれば、「書籍」と「レビュー」を同一集約に含めることによる問題よりも、集約によって整合性を確実に担保するメリットが勝るかもしれません。 class Book { ( 省略 ) addReview ( review : Review ) { //レビューの追加時にレビュー数の上限のチェックを行い、整合性を保つことができる if ( this . _reviews . length >= 100 ) { throw new Error ( 'レビューは100件までです。' ) ; } this . _reviews . push ( review ) ; } } しかし、レビュー数の上限が非常に大きい場合やそもそも上限が決められていない場合は、パフォーマンスやトランザクションのロックの問題が発生する可能性が高くなります。そのため、「書籍」と「レビュー」を別の集約にすることを検討する必要があります。 強整合性 (トランザクション整合性) が必要か 集約の範囲を決定する際、もう一つの重要な考慮事項は 強整合性 、つまり トランザクション整合性 の必要性です。すべてのデータが常に正確で最新の状態である必要がある場合、それらのデータは同じ集約内に含めるべきです。しかし、すべての場合において強い整合性が必要というわけではありません。 たとえば、同じく「書籍」と「レビュー」の関係で考えてみましょう。書籍情報は、タイトルや著者など重要なビジネスデータを含んでおり、これらの情報の整合性は重要です。一方で、レビューはユーザーによる個人的な意見や評価を含み、書籍の情報と直接的に関連するわけではありません。 また、レビュー数に上限があった場合はどうでしょう。レビュー数の整合性も重要になります。しかし、今回のケースでは仮に整合性が崩れ上限を超えてレビューが投稿されたとしても、ビジネス的にクリティカルな問題にはならないため、許容するという判断も可能でしょう。 このように本当に強整合性が必要なのかどうかをいくつかの要素で判断することができます。強整合性の必要性が低い、または必要ない場合はデータを別の集約として扱い整合性の要求を緩和することが可能です。「書籍」と「レビュー」の例で言えば、別の集約として扱ってもよいという判断ができます。 集約間は識別子(ID)で参照する ここでは、「書籍」と「レビュー」を別の集約として扱ってみましょう。集約をそれぞれ実装すると以下のようになります。 class Book { constructor ( private readonly _bookId : BookId , // 値オブジェクト private _title : Title , // 値オブジェクト private _author : Author , // エンティティ private _publisher : Publisher , // エンティティ ) { } ( 省略 ) } class Review { constructor ( private readonly _reviewId : ReviewId , // 値オブジェクト private _comment : Comment , // 値オブジェクト private _rating : Rating , // 値オブジェクト ) { } ( 省略 ) } しかし、これではレビュー集約がどの書籍に紐づくか (どの書籍に対してのレビューなのか) がわかりません。書籍とレビューを関連付けるためには、レビュー集約に書籍との関連性を持たせる必要があります。そこで以下のようにレビュー集約の属性に書籍集約を持たせることにしました。これでこのレビューがどの書籍に対してのレビューなのかがわかります。 class Review { constructor ( private readonly _reviewId : ReviewId , // 値オブジェクト private _comment : Comment , // 値オブジェクト private _rating : Rating , // 値オブジェクト private _book : Book , // ルートエンティティ(集約) ) { } ( 省略 ) } ですがこれはアンチパターンです。集約が関連性を表現するために集約を保持してはいけません。なぜなら、レビュー集約から書籍集約のメソッドを実行できてしまい、せっかく書籍集約内で保った整合性を破壊することが可能になってしまいます。整合性が破壊されることを防ぐには書籍集約とレビュー集約を同一のトランザクション内で処理する必要があります。それは 1 集約 1 トランザクション というルールに違反しており、最終的には大きな集約に逆戻りしてしまいます。 class Review { // レビュー集約から書籍集約のメソッドを実行できてしまう antiPatternMethod ( ) { this . _book . changeTitle ( '新しいタイトル' ) ; } } ではこの問題を解決するにはどうすれば良いでしょうか？ 集約間は識別子 (ID) で 参照する ことで解決されます。これでレビュー集約が直接書籍集約にアクセスすることができなくなりました。書籍集約にアクセスするにはリポジトリに一度問い合わせなければいけません。ドメインオブジェクトでリポジトリは利用できないため、実質的にアクセスできないことを意味します。これは大きなセーフティネットです。 class Review { constructor ( private readonly _reviewId : ReviewId , // 値オブジェクト private _comment : Comment , // 値オブジェクト private _rating : Rating , // 値オブジェクト private _bookId : BookId , // ルートエンティティ(集約)のID ) { } ( 省略 ) } これらの理由から集約間は識別子 (ID) で参照しなければなりません。 集約の外部では結果整合性を用いる ここでも「書籍」と「レビュー」を別の集約として扱ってみましょう。「書籍」と「レビュー」には 1対n の関係性がありましたね。 たとえば、1 つの書籍のレビューの上限が 100 件だとします。「書籍」と「レビュー」を別の集約として扱うということはそれぞれ別のトランザクションで更新するということです。つまり、整合性が崩れ 101 件目以降のレビューが作成されてしまう可能性があることを意味します。集約を別にするということはこの挙動を許容するということです。 ですが仮に 101 件のレビューが作成され、それがビジネス的にクリティカルな問題にはならないとしても、上限を 100 件と決めた理由があるでしょう。上限を超えた状態そのままで放置することは問題です。 このような場合に、 結果整合性 を用いることで整合性を担保することができます。結果整合性を簡単に説明すると、 一時的にデータの不整合が起きても、最終的には整合性が担保されていれば OK という考え方です。 結果整合性を保つ方法として、たとえば毎日バッチ処置で書籍のレビューを監視し、数が 100 件を超えていた場合、101 件目以降のレビューを無効にする処理を実行するという方法が考えられます。少し極端な例ですが、これによりレビュー数が 結果的 に 100 件以下になり、整合性が担保されます。 ! 集約を設計していると、本当に強整合性が必要なものは意外と少ないことに気づきます。本当に強整合性が必要なものだけ同一集約とし、それ以外は結果整合性を用いることを検討しましょう。そうすることで、小さな集約を設計することができます。 結果整合性を保つために ドメインイベント を活用することもあります。ドメインイベントについては chapter18 で説明します。 まとめ 集約とは、関連するオブジェクト群を 1 つのユニットとして管理するための手法 集約は、1 つのルートエンティティ (集約ルート) と、それに関連するエンティティや値オブジェクトで構成される 集約はなるべく小さくすることで、パフォーマンスやトランザクションのロックの問題を回避する 集約内は強整合性、集約間は結果整合性を用いる 本章では、集約の設計と実装について学びました。 次章は集約や値オブジェクトで表現できないドメイン知識を扱う ドメインサービス について学んでいきます。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/aggregate 参考文献 https://blog.j5ik2o.me/entry/2021/03/09/231332 PREV エンティティ (Entity) NEXT ドメインサービス (Domain Service) GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter15_scalability
================================================================================

このチャプターの目次 拡張性とメンテナンス まとめ 拡張性とメンテナンス ドメイン駆動設計の原則を実際のアプリケーション開発と維持にどのように応用するかを探求します。ここでは、システムの持続可能性と成長に重点を置き、長期にわたるメンテナンスと拡張性の確保を目的とします。 まとめ それでは次章から、拡張性とメンテナンスをテーマに、具体的なアプローチを詳しく見ていきましょう。 PREV プレゼンテーション (Presentation) NEXT ESLintで不正な依存関係を防ぐ GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter14_presentation
================================================================================

このチャプターの目次 プレゼンテーションとは Express.js を利用した実装 環境のセットアップ 書籍登録 API の実装 まとめ これまでのコード プレゼンテーションとは プレゼンテーションとは、アプリケーションの ユーザーインターフェイス 部分です。MVC で言うと、プレゼンテーション層は Controller に相当します。ユーザーの入力を受け取り、処理結果をわかりやすい形で返却します。 ユーザーインターフェイスにはたくさんの種類があります。たとえば Web アプリケーションにおいては、「HTML」「REST API」 「CLI」などが該当します。プレゼンテーションの文脈でいう ユーザー とは人間を指しているわけではありません。Web アプリケーションの場合はブラウザ、CLI の場合はコマンドライン、API の場合はリクエストを送信するクライアントなどです。 プレゼンテーションは、アプリケーションサービスのクライアントであり、ユーザーからの入力をアプリケーションサービスに渡します。そしてアプリケーションサービスからの処理結果を、ユーザーがわかりやすい形で返却します。「わかりやすい形」とは、たとえば表示に適した日付のフォーマットや、ユーザーが理解しやすいエラーメッセージなどです。 また、ドメイン層やアプリケーション層から投げられた例外をキャッチするのもプレゼンテーションの役割です。例外をキャッチしたら、ユーザーインターフェイスや例外の内容によって適切なハンドリングを行います。 ! プレゼンテーション層は、特定の技術や Web アプリケーションフレームワークに依存することが許可されます。それらの依存をドメイン層やアプリケーション層に持ち込まないように注意し、プレゼンテーション層に閉じ込めることが重要です。 Express.js を利用した実装 それでは、 Express.js を利用した API の実装を行い、 API を叩いてデータベースにデータを登録するまでの流れを確認します。 環境のセットアップ まずは、 Express.js を利用するための環境をセットアップします。 パッケージのインストール 必要なパッケージをインストールしていきます。 StockManagement/ $ npm install express
$ npm i --save-dev @types/express 動作確認 src ディレクトリ配下に Presentation/Express ディレクトリを作成します。次に、 index.ts ファイルを作成し、以下のように実装します。 src/Presentation/Express/index.ts import express from 'express' ; const app = express ( ) ; const port = 3000 ; app . get ( '/' , ( _ , res ) => { res . send ( 'Hello World!' ) ; } ) app . listen ( port , ( ) => { console . log ( ` Example app listening on port ${ port } ` ) ; } ) ; サーバの起動を行います。 StockManagement/ $ npx ts-node src/Presentation/Express/index.ts ブラウザで http://localhost:3000 にアクセスし、 Hello World! が表示されれば環境のセットアップは完了です。 書籍登録 API の実装 それでは、書籍登録 API の実装を行います。まずは、 API のエンドポイントを作成します。 index.ts ファイルに以下の実装を追加します。 src/Presentation/Express/index.ts import express from 'express' ; import { RegisterBookApplicationService , RegisterBookCommand , } from 'Application/Book/RegisterBookApplicationService/RegisterBookApplicationService' ; import { PrismaBookRepository } from 'Infrastructure/Prisma/Book/PrismaBookRepository' ; import { PrismaClientManager } from 'Infrastructure/Prisma/PrismaClientManager' ; import { PrismaTransactionManager } from 'Infrastructure/Prisma/PrismaTransactionManager' ; const app = express ( ) ; const port = 3000 ; app . get ( '/' , ( _ , res ) => { res . send ( 'Hello World!' ) ; } ) ; app . listen ( port , ( ) => { console . log ( ` Example app listening on port ${ port } ` ) ; } ) ; // JSON形式のリクエストボディを正しく解析するために必要 app . use ( express . json ( ) ) ; app . post ( '/book' , async ( req , res ) => { try { const requestBody = req . body as { isbn : string ; title : string ; priceAmount : number ; } ; const clientManager = new PrismaClientManager ( ) ; const transactionManager = new PrismaTransactionManager ( clientManager ) ; const bookRepository = new PrismaBookRepository ( clientManager ) ; const registerBookApplicationService = new RegisterBookApplicationService ( bookRepository , transactionManager ) ; // リクエストボディをコマンドに変換。今回はたまたま一致しているため、そのまま渡している。 const registerBookCommand : RegisterBookCommand = requestBody ; await registerBookApplicationService . execute ( registerBookCommand ) ; // 実際は詳細なレスポンスを返す res . status ( 200 ) . json ( { message : 'success' } ) ; } catch ( error ) { // 実際はエラーを解析し、詳細なレスポンスを返す。また、ロギングなどを行う。 res . status ( 500 ) . json ( { message : ( error as Error ) . message } ) ; } } ) ; API のエンドポイントは /book です。 POST メソッドでリクエストを受け取り、 RegisterBookApplicationService のコマンドの型に合わせてリクエストボディを変換しています。 RegisterBookApplicationService には、 Prisma を利用した BookRepository と TransactionManager を注入しています。その後実行し、結果をレスポンスとして返却しています。 動作確認 それでは、実際に API のエンドポイントにリクエストを送信し、動作確認を行います。データベースが立ち上がっていない場合は、 docker-compose up -d であらかじめ立ち上げておきましょう。 ここでは curl コマンドを利用してリクエストを送信します。 StockManagement/ $ curl -X POST -H "Content-Type: application/json" -d '{"isbn":"9784167158057","title":"吾輩は猫である","priceAmount":770}' http://localhost:3000/book { "message" : "success" } {"message":"success"} というレスポンスが返却されれば、正しく登録が完了しています。 prisma では、データベース内のデータを確認するための GUI が用意されています。 npx prisma studio を実行すると、 http://localhost:5555 で GUI が立ち上がります。Book テーブルを確認し、データが登録されていることを確認します。 次に、再度同じリクエストを送信してみます。 StockManagement/ $ curl -X POST -H "Content-Type: application/json" -d '{"isbn":"9784167158057","title":"吾輩は猫である","priceAmount":770}' http://localhost:3000/book { "message" : "既に存在する書籍です" } {"message":"既に存在する書籍です"} というレスポンスが返却されデータの登録に失敗します。これは、ISBN が一意であることを保証するために、 RegisterBookApplicationService 内でドメインサービスを利用しチェックを行っているためです。エラーのメッセージは、ドメインサービスで定義したものがそのまま返却されています。 レスポンスは、 API の仕様に合わせて適切に定義する必要があります。ここでは、特に考慮せずに簡易的に実装していますが、実際には仕様に合わせてレスポンスを定義する必要があります。 取得や更新、削除などのエンドポイントも同様に実装して行くことができます。 以上で、 Express.js を利用した API の実装は完了とします。 まとめ プレゼンテーション層は、アプリケーションのユーザーインターフェイスである 本章では、プレゼンテーション層の実装を行いました。 Express.js を利用した API の実装を行い、 API を叩いてデータベースにデータを登録するまでの流れを確認しました。この本ではプレゼンテーションの一例として API の実装を行いましたが、 CLI などのその他プレゼンテーションの実装も同様です。リクエストを受け取り、データを変換しアプリケーションサービスを呼び出し、ユーザーに沿った結果を返却するという流れは同じです。 以上でドメイン駆動設計の基本的な戦術的設計 (コード実装) は終了です。お疲れ様でした！ここまでの実装を通して、ドメイン駆動設計の特有な考え方や実装方法を学びました。実際の開発現場でドメイン駆動設計を利用する際に、この本で学んだことを活かしていただければ幸いです。 次章からは、拡張性とメンテナンスについて学んでいきます。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/presentation PREV アプリケーションサービス (Application Service) NEXT 第3部 拡張性とメンテナンス GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter7_tactical_design
================================================================================

このチャプターの目次 戦術的設計とは アーキテクチャ オニオンアーキテクチャとは オニオンアーキテクチャの特徴 依存性逆転の原則 (DIP） 環境のセットアップ npm プロジェクトの初期化 src ディレクトリの作成 パッケージのインストール TypeScript の設定 TypeScript の動作確認 Jest の設定 Jest の動作確認 まとめ これまでのコード 戦術的設計とは 戦術的設計とは、ビジネスの複雑性をコードにどのように反映させるか、具体的なアプローチやパターンに焦点を当てたものです。第 1 部で行ったドメインモデリングでの成果物をドメイン駆動設計のアプローチやパターン (値オブジェクト、エンティティ、集約、リポジトリなど) を紹介すると共に実際にコードに反映していきます。 アーキテクチャ ここでアーキテクチャに触れておきましょう。ドメイン駆動設計には固有のアーキテクチャはありません。ここでのアーキテクチャとは、ソフトウェアアプリケーションを構築する際の設計思想と構造計画であり、ドメイン駆動設計のアプローチやパターンをどのように実装するかを決めるものです。良いアーキテクチャは、要件の変更や新しい技術の採用に柔軟に対応し、システムのパフォーマンス、スケーラビリティ、セキュリティを最適化する手助けとなります。 アーキテクチャにはレイヤードアーキテクチャ、オニオンアーキテクチャ、ヘキサゴナルアーキテクチャ、クリーンアーキテクチャなどさまざまな種類があります。この本ではドメイン駆動設計のアーキテクチャに オニオンアーキテクチャ を採用しています。 オニオンアーキテクチャとは オニオンアーキテクチャは、ソフトウェアの設計パターンの一つで、アプリケーションを複数のレイヤーで構成し、それらを円形に配置することを特徴とします。このアーキテクチャは、ドメイン駆動設計との相性が良く、ドメイン駆動設計のアプローチやパターンを実装するのに適しています。 オニオンアーキテクチャの特徴 オニオンアーキテクチャは一般的に以下のような図で表現されます。 この図を参考にオニオンアーキテクチャの特徴を見ていきましょう。 ドメイン中心の設計 オニオンアーキテクチャは、アプリケーションの中心にドメインモデル（ビジネスロジック）を置き、他の層はこのドメインモデルを取り囲む形で構築されます。 ドメイン層が他の層に依存しない ようにすることで、ドメインモデルを独立して開発することができます。 レイヤー構造 オニオンアーキテクチャでは、アプリケーションを複数のレイヤーに分割します。ドメイン駆動設計において各レイヤーにはそれぞれ以下のような役割があります。 レイヤー 説明 ドメイン層 これはアプリケーションのコアであり、ビジネスルールやビジネスロジックを表現します。主にエンティティ、値オブジェクト、ドメインサービス、およびリポジトリのインターフェイスが含まれます。 アプリケーション層 アプリケーション層はドメイン層のクライアントです。ユースケースを組み立てるためにドメイン層を使用します。主にアプリケーションサービスが含まれます。 インフラストラクチャ層 この層はアプリケーションに必要な外部リソース (データベース、ファイルシステム、外部サービス) への通信を担当します。主にリポジトリの実装が含まれます。 プレゼンテーション層 ユーザーのリクエストを受け取り、適切な応答を返します。主に Web UI 、 REST API 、 CLI などが含まれます。 依存関係の方向の制限 オニオンアーキテクチャでは、依存関係に明確な方向性があります。このアーキテクチャは、外側のレイヤー（アプリケーション層やインフラ層）から内側のレイヤー（ドメイン層）への一方向の依存関係を強制します。このようにすることで、以下のようなメリットがあります。 ドメイン層の独立性 ドメイン層はアプリケーションのビジネスロジックの中心であり、他の層に依存しないようにすることが重要です。これにより、ビジネスロジックが他の層の変更に影響を受けることがなくなり、ドメインモデルを独立して開発することができます。 テストの容易性 ドメイン層が他の層に依存しないため、単体テストが容易になります。ビジネスロジックを他の層から分離することで、モックやスタブを使用せずにテストを行うことができ、テストの容易性が向上します。 拡張性と保守性の向上 各層が特定の責任を持ち、依存関係を制御することで、システムの変更が容易になります。たとえば、インフラストラクチャ層の変更がドメイン層に影響を及ぼすことはありません。これにより、新しい機能の追加や既存機能の変更がより容易になります。 再利用性の向上 ドメイン層が独立しているため、ビジネスロジックを使い回すことができます。これは、ドメイン層がアプリケーションやインフラストラクチャの特定の実装に依存しないことによるものです。 依存性逆転の原則 (DIP） オニオンアーキテクチャにおける依存関係の方向性は、「依存性逆転の原則 (Dependency Inversion Principle, DIP) 」というソフトウェア設計の原則を反映しています。この原則は以下の二つのポイントで構成されています。 上位モジュールは下位モジュールに依存してはならない。どちらのモジュールも抽象に依存すべきである ビジネスロジックを含む上位モジュール (ドメイン層) は、データアクセスや外部 API のような下位モジュール (インフラストラクチャ層やプレゼンテーション層) に依存してはいけません、。そして、どちらのモジュールも抽象に依存すべきです。 抽象は詳細に依存してはならない。詳細が抽象に依存すべきである 抽象 (インターフェイスや抽象クラス) は詳細 (具体的な実装) に依存せず、詳細は抽象に依存すべきです。 具体的には「依存性の注入 (Dependency injection, DI) 」を利用することで、依存関係を制御します。 DI (Dependency injection) とは DI とは、ソフトウェア設計の手法であり、コンポーネント間の依存関係を外部から注入することによって、コードの柔軟性と再利用性を高めるアプローチです。具体的には、あるオブジェクトが必要とする依存オブジェクト（サービス、ツール、クライアントなど）を、そのオブジェクトの内部で生成するのではなく、外部から提供 (注入) することを意味します。 DI を用いると何が嬉しいのかを簡単な例で見てみましょう。以下のコードでは、 sayHello 関数が logger を呼び出しています。 const sayHello = ( name : string ) : void => { logger ( ` Hello ${ name } ! ` ) ; } ; sayHello ( 'World' ) ; このコードは、 sayHello 関数が logger を呼び出しているため、 logger の実装を変更すると sayHello 関数に影響を与えます。これは、 sayHello 関数が logger に強く依存しているためです。この問題を解決するために、DI を利用します。 const sayHello = ( name : string , logger : ( message : string ) => void ) : void => { logger ( ` Hello ${ name } ! ` ) ; } ; sayHello ( 'World' , console . log ) ; sayHello 関数は、 logger を注入することで、 logger の実装を変更しても sayHello 関数に影響を与えなくなります。これにより、ロギングツールが決まるまで console.log で代用するることができたり、ロギングツールの変更が容易になるなどのメリットがあります。 このように、DI を利用することで、依存関係の方向性を制御し、依存性逆転の原則を実現します。依存性逆転の原則を適用することで、結果としてシステムの保守性、拡張性、テスト容易性が向上します。 ! オニオンアーキテクチャを利用してレイヤーを分離し、依存関係を制御する事によるメリットは、まだあまり感じられていないでしょう。第 2 部では DI を利用するシーンが多く登場します。実際にコードを書きながら理解していきましょう。 環境のセットアップ 次の章に進む前に、 TypeScript の環境をセットアップしましょう。 node のバージョンは 「18.15.0」 です。 $ node -v v18.15.0 npm プロジェクトの初期化 StockManagement/ $ npm init -y src ディレクトリの作成 src ディレクトリを作成し Domain ディレクトリを src ディレクトリ配下に移動します。 StockManagement/ $ mkdir src && mv Domain/ src/ パッケージのインストール 必要なパッケージをインストールしていきます。 StockManagement/ $ npm i -D typescript ts-node tsconfig-paths @types/node jest ts-jest @types/jest TypeScript の設定 tsconfig.json を作成し以下のように、設定を行なってください。オプションはお好みで追加してください。 StockManagement/tsconfig.json { "ts-node" : { "require" : [ "tsconfig-paths/register" ] // ts-nodeがtsconfigのpathsを解決できるようにします。 } , "compilerOptions" : { "outDir" : "./dist" , "strict" : true , "resolveJsonModule" : true , "noUnusedLocals" : true , "noUnusedParameters" : true , "baseUrl" : "./" , "paths" : { "*" : [ "./src/*" ] } , "esModuleInterop" : true } , "include" : [ "src/**/*.ts" , "src/**/*.js" ] , "exclude" : [ "node_modules" ] } TypeScript の動作確認 sayHello.ts を作成し以下のようにコードを記述します。 src/sayHello.ts export const sayHello = ( name : string ) : string => { const res = ` Hello ${ name } ! ` console . log ( res ) ; return res ; } ; sayHello ( 'World' ) ; StockManagement/ $ ts-node src/sayHello.ts ts-node コマンドで実行し、ターミナルに「Hello World!」と表示されば OK です。 Jest の設定 テストには jest を利用します。まず jest.config.js を作成し以下のように設定を行なってください。 StockManagement/jest.config.js /** @type { import ( 'ts-jest' ) . JestConfigWithTsJest } */ module . exports = { preset : 'ts-jest' , testEnvironment : 'node' , moduleDirectories : [ 'node_modules' , 'src' ] , transformIgnorePatterns : [ '/node_modules' ] , } ; Jest の動作確認 src ディレクトリ配下に sayHello.test.ts を作成し以下のように実装します。 src/sayHello.test.ts import { sayHello } from './sayHello' ; test ( 'sayHello' , ( ) => { expect ( sayHello ( 'World' ) ) . toBe ( 'Hello World!' ) ; } ) ; StockManagement/ $ jest src/sayHello.test.ts jest コマンドでテストを実行し、テストに成功すれば OK です。 sayHello.ts と sayHello.test.ts は以降使用しないため削除しましょう。 まとめ 本章では、戦術的設計のアプローチやパターンを紹介し、オニオンアーキテクチャを採用した理由を説明しました。また、オニオンアーキテクチャの特徴や依存性逆転の原則についても説明しました。最後に、環境のセットアップを行いました。 次章から戦術的設計の各アプローチををれぞれ詳しく確認しながらコードの実装を行なっていきましょう。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/setup PREV ドメインモデル図の作成 NEXT 値オブジェクト (Value Object) GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter17_di_container
================================================================================

このチャプターの目次 DI コンテナとは tsyringe を利用して DI コンテナを作成する 環境のセットアップ デコレータの設定 依存オブジェクトの登録 依存オブジェクトの解決 まとめ これまでのコード DI コンテナとは DI コンテナとは、オブジェクトのインスタンス化と依存関係の解決を行うコンテナのことです。 これまで、依存性逆転の原則 (DIP) に従って、依存関係を注入 (DI) することで、オブジェクトの拡張性やテストの容易性を向上させることについて学んできました。 たとえば、アプリケーションサービスでは、リポジトリをコンストラクタインジェクションで注入することで、特定の技術への依存を排除し、アプリケーションサービスのテストを容易にしました。 これまでの実装では、オブジェクトのインスタンス化を行う際にコンストラクタに依存オブジェクトを明示的に渡していました。たとえば、アプリケーションサービスではリポジトリを DI する必要があり、テストでは明示的にインメモリのリポジトリをインスタンス化し、コンストラクタに渡していました。しかし、この方法では、依存オブジェクトのインスタンス化と依存関係の解決をアプリケーションサービスのコンストラクタの外側で行う必要があります。このため、依存オブジェクトのインスタンス化と依存関係の解決を行うコードが散在してしまい、コードの可読性が低下してしまいます。 また、開発時にはインメモリデータベースを利用し、本番環境では PostgreSQL を利用するように切り替えたい場合、リポジトリのインスタンス化を行っている箇所をすべて洗い出し、差し替えていく必要があります。このような作業は、ヒューマンエラーを誘発する原因となります。 DI コンテナ を利用することで、依存オブジェクトのインスタンス化と依存関係の解決を行うコードを集約することができます。また、差し替えも DI コンテナの設定を変更するだけで済むため、容易になります。 tsyringe を利用して DI コンテナを作成する それでは実際に DI コンテナを利用してみましょう。ここでは、 Microsoft が提供している TypeScript/JavaScript 用の DI コンテナライブラリ tsyringe を利用します。 環境のセットアップ まずは tsyringe の環境をセットアップしましょう。 パッケージのインストール 必要なパッケージをインストールしていきます。 $ npm install --save tsyringe reflect-metadata 初期設定 次の設定を含むように tsconfig.json を変更します StockManagement/tsconfig.json { "ts-node" : { "require" : [ "tsconfig-paths/register" ] // ts-nodeがtsconfigのpathsを解決できるようにします。 } , "compilerOptions" : { "outDir" : "./dist" , "strict" : true , "resolveJsonModule" : true , "noUnusedLocals" : true , "noUnusedParameters" : true , "baseUrl" : "./" , "paths" : { "*" : [ "./src/*" ] } , "esModuleInterop" : true , + "experimentalDecorators" : true , + "emitDecoratorMetadata" : true } , "include" : [ "src/**/*.ts" , "src/**/*.js" ] , "exclude" : [ "node_modules" ] } テストでポリフィルの影響を受けるため、テストが開始される前にポリフィルを読み込むような設定を行います。 setupJest.ts ファイルを作成し、以下のように実装します。 setupJest.ts import 'reflect-metadata' ; jest では jest.config.js の setupFilesAfterEnv を利用してテストが実行される前に適当な処理を行うことができます。 setupJest.ts を読み込むように設定を変更します。 StockManagement/jest.config.js /** @type { import ( 'ts-jest' ) . JestConfigWithTsJest } */ module . exports = { preset : 'ts-jest' , testEnvironment : 'node' , moduleDirectories : [ 'node_modules' , 'src' ] , transformIgnorePatterns : [ '/node_modules' ] , + setupFilesAfterEnv : [ './setupJest.ts' ] , }; 以上で環境のセットアップは完了です。 tsyringe を利用し、依存オブジェクトのインスタンス化と依存関係の解決を行うためには、以下のような設定が必要となります。 デコレータの設定 依存オブジェクトの登録 依存オブジェクトの解決 それでは、これらの設定を行なっていきましょう。 デコレータの設定 まず初めに、依存関係として DI されるクラスに @injectable() デコレータを適用します。次にコンストラクタインジェクションする引数に @inject('Interface') デコレータを適用します。これにより、特定のインターフェイスに対応する依存オブジェクトを DI コンテナを利用して注入することができます。依存オブジェクトとは、たとえばアプリケーションサービスで実行時に利用したいリポジトリを指します。 それでは 依存関係として DI されるクラスに対して上記の設定を行なっていきましょう。 src/Infrastructure/Prisma/PrismaTransactionManager.ts + import { injectable , inject } from 'tsyringe' ; ... + @ injectable ( ) export class PrismaTransactionManager implements ITransactionManager { constructor ( + @ inject ( 'IDataAccessClientManager' ) private clientManager : PrismaClientManager ) { } ( 省略 ) } src/Infrastructure/Prisma/Book/PrismaBookRepository.ts + import { injectable , inject } from 'tsyringe' ; ... + @ injectable ( ) export class PrismaBookRepository implements IBookRepository { constructor ( + @ inject ( 'IDataAccessClientManager' ) private clientManager : PrismaClientManager ) { } ( 省略 ) } ...src/Application/Book/RegisterBookApplicationService/RegisterBookApplicationService.ts + import { injectable , inject } from 'tsyringe' ; ... + @ injectable ( ) export class RegisterBookApplicationService { constructor ( + @ inject ( 'IBookRepository' ) private bookRepository : IBookRepository , + @ inject ( 'ITransactionManager' ) private transactionManager : ITransactionManager ) { } ( 省略 ) } そのほかのアプリケーションサービスに対しても同様にデコレータの設定を行なってください。 依存オブジェクトの登録 次に、依存オブジェクトの登録を行います。登録には tsyringe が提供する container.register メソッドを利用します。 src/ 配下に依存オブジェクト登録用に Program.ts ファイルを作成し、以下のように実装します。 src/Program.ts import { container , Lifecycle } from 'tsyringe' ; import { PrismaBookRepository } from 'Infrastructure/Prisma/Book/PrismaBookRepository' ; import { PrismaClientManager } from 'Infrastructure/Prisma/PrismaClientManager' ; import { PrismaTransactionManager } from 'Infrastructure/Prisma/PrismaTransactionManager' ; // repository container . register ( 'IBookRepository' , { useClass : PrismaBookRepository , } ) ; // transactionManager container . register ( 'ITransactionManager' , { useClass : PrismaTransactionManager , } ) ; // IDataAccessClientManager container . register ( 'IDataAccessClientManager' , { useClass : PrismaClientManager , } , // The same instance will be resolved for each resolution of this dependency during a single resolution chain { lifecycle : Lifecycle . ResolutionScoped } ) ; container.register の第一引数では登録したいオブジェクトのインターフェイスを指定します。第二引数では、登録したいオブジェクトの実装クラスを指定します。第三引数では、生成するオブジェクトのインスタンスのライフサイクルを指定することができます。ライフサイクルについての説明は こちら を参照してください。 この設定により、 container.resolve を利用してインスタンス化を行なった場合、たとえば IBookRepository を DI している箇所で PrismaBookRepository のインスタンス化が行なわれます。 テスト時にはインメモリのリポジトリを使いたくなるでしょう。その場合は、 Program.ts で登録した IBookRepository の実装クラスを PrismaBookRepository から InMemoryBookRepository に差し替えることで、リポジトリを利用する側のコードに変更を加えずに、インメモリのリポジトリを利用することができます。もしくはテスト用の Program ファイルを作成し、テスト実行前にテスト用の Program ファイルを読み込ませることによっても同様のことができます。 依存オブジェクトの解決 container.register によって登録された依存オブジェクトは、 container.resolve を利用して解決することができます。 それでは、 RegisterBookApplicationService のインスタンス化を container.resolve を利用して行なってみましょう。 src/Express/index.ts + // Reflectのポリフィルをcontainer.resolveされる前に一度読み込む必要がある + import 'reflect-metadata' ; + import '../../Program' ; + import { container } from 'tsyringe' ; ... app . post ( '/book' , async ( req , res ) => { try { const requestBody = req . body as { isbn : string ; title : string ; priceAmount : number ; } ; - const clientManager = new PrismaClientManager ( ) ; - const transactionManager = new PrismaTransactionManager ( clientManager ) ; - const bookRepository = new PrismaBookRepository ( clientManager ) ; - const registerBookApplicationService = new RegisterBookApplicationService ( - bookRepository , - transactionManager - ) ; + const registerBookApplicationService = container . resolve ( + RegisterBookApplicationService + ) ; // リクエストボディをコマンドに変換。今回はたまたま一致しているため、そのまま渡している。 const registerBookCommand : RegisterBookCommand = requestBody ; await registerBookApplicationService . execute ( registerBookCommand ) ; // 実際は詳細なレスポンスを返す res . status ( 200 ) . json ( { message : 'success' } ) ; } catch ( error ) { // 実際はエラーを解析し、詳細なレスポンスを返す。また、ロギングなどを行う。 res . status ( 500 ) . json ( { message : ( error as Error ) . message } ) ; } } ) ; まず初めに、 Reflect のポリフィルの読み込みと、さきほど定義した Program.ts の読み込みを行なう必要があります。 次に、 RegisterBookApplicationService のインスタンス化を container.resolve で行なっています。これにより、 RegisterBookApplicationService のインスタンス化に必要な依存オブジェクト (リポジトリなど) のインスタンス化と依存関係の解決が Program.ts で登録した設定に従って行なわれるようになります。 そして、リポジトリやトランザクション管理オブジェクトを明示的にインスタンス化するコードが無くなったことに注目してください。つまり、依存オブジェクトのインスタンス化と依存関係の解決を行うコードが Program.ts に集約されたということです。これにより、依存オブジェクトのインスタンス化と依存関係の解決を行うコードが散在することなく、コードの可読性が向上しました。また、差し替えも Program.ts を変更するだけで済むため、容易になりました。 以上で、DI コンテナを利用した実装への変更は完了です。 まとめ DI コンテナを利用することで、散在していた依存オブジェクトのインスタンス化と依存関係の解決を行うコードを集約することができる DI コンテナを利用することで、コードの可読性の向上と依存オブジェクトの差し替えを容易にすることができる 本章では、DI コンテナを利用して依存オブジェクトのインスタンス化と依存関係の解決を行う方法について学びました。DI コンテナを利用することで、依存オブジェクトのインスタンス化と依存関係の解決を行うコードを集約することができ、コードの可読性が向上しました。また、差し替えも Program.ts を変更するだけで済むため、容易になりました。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/DIContainer PREV ESLintで不正な依存関係を防ぐ NEXT ドメインイベントの活用 GitHubで編集を提案


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter16_eslint
================================================================================

このチャプターの目次 不正な依存関係 環境のセットアップ 不正な依存関係の洗い出し ESLint で不正な依存関係を防ぐ まとめ これまでのコード 不正な依存関係 第 2 部では、ドメイン層を独立して実装するために、「 ドメイン層がインフラストラクチャや特定の技術に依存しないようにしましょう 」と繰り返し言ってきました。具体的な対応としては、オニオンアーキテクチャと依存性逆転の原則 (DIP) を適用し、ドメイン層への依存を防ぐような構成を用いました。 しかし、これらの制約は口約束でしかなく、強制力がありません。チームで開発している場合、コードレビューで指摘することも可能ですが、レビュー漏れは起こりえます。極端な例ですが以下のようにドメイン層でインフラストラクチャのコードを呼び出してしまうことも可能です。 .../src/Domain/models/Book/Book.ts import prisma from 'Infrastructure/Prisma/prismaClient' ; export class Book { private constructor ( private readonly _bookId : BookId , private _title : Title , private _price : Price , private readonly _stock : Stock ) { } // ドメイン層のエンティティでprismaを利用しDBを操作できてしまう async insertDB ( book : Book ) { await prisma . book . create ( { data : { bookId : book . bookId . value , title : book . title . value , priceAmount : book . price . value . amount , } , } ) ; } } これでは、せっかく保っていたドメイン層の独立性が崩れてしまいます。そこで ESLint でのアプローチを利用することで、このような不正な依存関係を防ぐことができます。 環境のセットアップ まずは、 ESLint の環境をセットアップしましょう。詳しくは こちら を参照してください。 パッケージのインストール 必要なパッケージをインストールしていきます。 $ npm install --save-dev eslint eslint-plugin-import eslint-import-resolver-typescript @typescript-eslint/eslint-plugin 初期設定 ルートディレクトリに .eslintrc.js ファイルを作成し、以下のように設定します。ここでは最低限の設定のみ行います。 stockManagement/.eslintrc.js module . exports = { env : { node : true , es6 : true , } , extends : [ 'plugin:import/recommended' , 'plugin:import/typescript' , 'plugin:@typescript-eslint/recommended' , ] , parserOptions : { ecmaVersion : 'latest' , sourceType : 'module' , } , // TypeScriptの場合に必要な設定 settings : { 'import/resolver' : { typescript : { } , } , } , } ; これでセットアップは完了です。 不正な依存関係の洗い出し 設定の前に、再度オニオンアーキテクチャの図を確認し、不正な依存関係を洗い出しておきましょう。 ドメイン層が依存してはいけない層 アプリケーション層 インフラストラクチャ層 プレゼンテーション層 アプリケーション層が依存してはいけない層 インフラストラクチャ層 プレゼンテーション層 これらが不正な依存関係となります。 実装において、依存してはいけないということは、import してはいけないということに言い換えることができます。ですが、import すること自体を防ぐことはできないため、不正な import がされた時にエラーを出し、検知できるような仕組みを ESLint を利用して構築していきます。 ESLint で不正な依存関係を防ぐ ESLint には、さまざまなプラグインが用意されています。その中でも eslint-plugin-import というプラグインの import/no-restricted-paths というルールを利用し、以下のように設定します。 stockManagement/.eslintrc.js module . exports = { ( 省略 ) rules : { 'import/no-restricted-paths' : [ 'error' , { zones : [ // Domain層が依存してはいけない領域 { from : './src/Application/**/*' , target : './src/Domain/**/!(*.spec.ts|*.test.ts)' , message : 'Domain層でApplication層をimportしてはいけません。' , } , { from : './src/Presentation/**/*' , target : './src/Domain/**/!(*.spec.ts|*.test.ts)' , message : 'Domain層でPresentation層をimportしてはいけません。' , } , { from : './src/Infrastructure/**/*!(test).ts' , target : './src/Domain/**/!(*.spec.ts|*.test.ts)' , message : 'Domain層でInfrastructure層をimportしてはいけません。' , } , // Application層が依存してはいけない領域 { from : './src/Presentation/**/*' , target : './src/Application/**/!(*.spec.ts|*.test.ts)' , message : 'Application層でPresentation層をimportしてはいけません。' , } , { from : './src/Infrastructure/**/*' , target : './src/Application/**/!(*.spec.ts|*.test.ts)' , message : 'Application層でInfrastructure層をimportしてはいけません。' , } , ] , } , ] , } , } ; import/no-restricted-paths ルールで使用される 「from」, 「target」, 「message」 の各フィールドは、次のような役割を持っています。 フィールド 説明 from このフィールドは、制限を設けたいソースの場所 (ディレクトリまたはファイル) を指定します。この設定では、特定のディレクトリからの import を監視したい場合にこのフィールドを使用します。 target このフィールドは、 from で指定されたソースから import されるべきでないターゲットの場所（ディレクトリまたはファイル）を定義します。たとえば、ドメイン層のファイルがインフラストラクチャ層のファイルを import してはならない場合、ドメイン層を target として指定します。 message このフィールドは、ルールに違反した際に表示されるエラーメッセージをカスタマイズします。このメッセージは、開発者に対して何が問題であるかを具体的に説明し、アーキテクチャの設計原則を遵守するよう促すために使用されます。 設定が完了したので、実際に不正な依存関係の import を行い動作確認をしてみましょう。 src/Domain/models/Book/Book.ts import prisma from 'Infrastructure/Prisma/prismaClient' ; ドメイン層の Book.ts にインフラストラクチャ層の PrismaClient の import を追加しました。赤い波線が出てくるのでマウスカーソルを合わせると、 Unexpected path "Infrastructure/Prisma/prismaClient" imported in restricted zone. Domain層でInfrastructure層をimportしてはいけません。 というエラーが表示されます。 ESLint の import/no-restricted-paths ルールを適切に設定することで、誤って不正な依存関係を持つ実装が行われた際にエラーが発生するようになります。これにより、開発者は実装時に即座に問題を検知できます。さらに、連続的インテグレーション (CI) 環境で ESLint を実行することにより、不正な依存関係を持つコードがリポジトリにマージされることを効果的に防ぐことが可能です。このような手法により、プロジェクトのアーキテクチャと品質を維持するための重要な安全網を提供することができます。 まとめ 本章では、 ESLint を利用して不正な依存関係を防ぐ方法、つまり不正な import を防ぐ方法を学びました。 ESLint は、依存関係を制御し、ドメイン層の独立性を保つために必須のツールと言えるでしょう。 これまでのコード https://github.com/yamachan0625/ddd-hands-on/tree/eslint PREV 第3部 拡張性とメンテナンス NEXT DI コンテナで依存関係を切り替える GitHubで編集を提案

