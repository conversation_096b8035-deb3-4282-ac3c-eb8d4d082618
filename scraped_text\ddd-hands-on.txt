
================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on
================================================================================

Chapters Chapter 01 はじめに Chapter 02 ドメイン駆動設計 (DDD) とは Chapter 03 第1部 戦略的設計 (ドメインモデリング) Chapter 04 ドメインモデリング Chapter 05 イベントストーミング Chapter 06 ドメインモデル図の作成 Chapter 07 第2部 戦術的設計 (コード実装) Chapter 08 値オブジェクト (Value Object) Chapter 09 エンティティ (Entity) Chapter 10 集約 (Aggregate) Chapter 11 ドメインサービス (Domain Service) Chapter 12 リポジトリ (Repository) Chapter 13 アプリケーションサービス (Application Service) Chapter 14 プレゼンテーション (Presentation) Chapter 15 第3部 拡張性とメンテナンス Chapter 16 ESLintで不正な依存関係を防ぐ Chapter 17 DI コンテナで依存関係を切り替える Chapter 18 ドメインイベントの活用


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter9_entity
================================================================================

Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 09 エンティティ (Entity) ヤマユ 2024.05.08に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter17_di_container
================================================================================

Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 17 DI コンテナで依存関係を切り替える ヤマユ 2024.01.21に更新


================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter4_domain_modeling
================================================================================

Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 04 ドメインモデリング ヤマユ 2024.01.12に更新

