
================================================================================
URL: https://zenn.dev/yamachan0625/books/ddd-hands-on/viewer/chapter6_model
================================================================================

【DDD入門】TypeScript × ドメイン駆動設計ハンズオン Zenn 【DDD入門】TypeScript × ドメイン駆動設計ハンズオン 01 はじめに 02 ドメイン駆動設計 (DDD) とは 03 第1部 戦略的設計 (ドメインモデリング) 04 ドメインモデリング 05 イベントストーミング 06 ドメインモデル図の作成 07 第2部 戦術的設計 (コード実装) 08 値オブジェクト (Value Object) 09 エンティティ (Entity) 10 集約 (Aggregate) 11 ドメインサービス (Domain Service) 12 リポジトリ (Repository) 13 アプリケーションサービス (Application Service) 14 プレゼンテーション (Presentation) 15 第3部 拡張性とメンテナンス 16 ESLintで不正な依存関係を防ぐ 17 DI コンテナで依存関係を切り替える 18 ドメインイベントの活用 Chapter 06 ドメインモデル図の作成 ヤマユ 2024.01.14に更新

